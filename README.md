# EventTopia - Online Event Booking System

EventTopia is a comprehensive online event booking platform built with the LAMP stack (Linux, Apache, MySQL, PHP). Users can browse, search, and book tickets for various events while administrators can manage events, bookings, and generate reports.

## Features

### User Features
- **User Authentication**: Sign-up, login, and session management
- **Event Browsing**: Browse events with search and filter functionality
- **Event Details**: Detailed event information with venue details
- **Shopping Cart**: Add multiple events to cart and manage quantities
- **Secure Checkout**: Complete booking process with payment simulation
- **User Dashboard**: View booking history and manage profile
- **Responsive Design**: Mobile-friendly interface using Bootstrap

### Admin Features
- **Admin Dashboard**: Overview of platform statistics
- **Event Management**: Create, edit, delete, and manage events
- **Booking Management**: View and manage all bookings
- **User Management**: View registered users
- **Reports**: Generate reports by date, event, or user

## Technology Stack

- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript, jQuery
- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Web Server**: Apache 2.4+
- **Additional Libraries**: Font Awesome, PDO for database operations

## Installation Instructions

### Prerequisites
- Linux operating system
- Apache web server
- MySQL database server
- PHP 8.0 or higher with PDO extension

### Step 1: Install LAMP Stack

#### On Ubuntu/Debian:
```bash
sudo apt update
sudo apt install apache2 mysql-server php php-mysql php-pdo libapache2-mod-php
```

#### On CentOS/RHEL:
```bash
sudo yum update
sudo yum install httpd mariadb-server php php-mysql php-pdo
```

### Step 2: Configure Apache

1. Enable Apache and start the service:
```bash
sudo systemctl enable apache2
sudo systemctl start apache2
```

2. Enable mod_rewrite (if needed):
```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### Step 3: Configure MySQL

1. Start MySQL service:
```bash
sudo systemctl enable mysql
sudo systemctl start mysql
```

2. Secure MySQL installation:
```bash
sudo mysql_secure_installation
```

3. Create database and user:
```sql
mysql -u root -p
CREATE DATABASE eventtopia;
CREATE USER 'eventtopia_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON eventtopia.* TO 'eventtopia_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 4: Deploy EventTopia

1. Clone or copy the EventTopia files to your web directory:
```bash
sudo cp -r EventTopia/ /var/www/html/
sudo chown -R www-data:www-data /var/www/html/EventTopia/
sudo chmod -R 755 /var/www/html/EventTopia/
```

2. Update database configuration in `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'eventtopia_user');
define('DB_PASS', 'your_password');
define('DB_NAME', 'eventtopia');
```

3. Import the database schema:
```bash
mysql -u eventtopia_user -p eventtopia < /var/www/html/EventTopia/database.sql
```

### Step 5: Configure Permissions

```bash
sudo chown -R www-data:www-data /var/www/html/EventTopia/
sudo chmod -R 755 /var/www/html/EventTopia/
sudo chmod -R 777 /var/www/html/EventTopia/assets/images/
```

### Step 6: Access the Application

1. Open your web browser and navigate to:
   - Main site: `http://your-server-ip/EventTopia/`
   - Admin panel: `http://your-server-ip/EventTopia/admin/`

2. Default admin credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`

## Project Structure

```
EventTopia/
├── config/                 # Configuration files
│   ├── database.php       # Database connection
│   └── config.php         # Application settings
├── includes/              # Common includes
│   ├── header.php         # HTML header
│   ├── footer.php         # HTML footer
│   ├── navbar.php         # Navigation bar
│   └── functions.php      # Common functions
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Images
├── ajax/                  # AJAX endpoints
│   ├── add_to_cart.php   # Add items to cart
│   ├── update_cart.php   # Update cart quantities
│   └── remove_from_cart.php # Remove cart items
├── admin/                 # Admin panel
│   ├── index.php         # Admin dashboard
│   ├── events.php        # Event management
│   ├── add_event.php     # Add new events
│   └── bookings.php      # Booking management
├── user/                  # User dashboard
│   ├── dashboard.php     # User dashboard
│   ├── profile.php       # User profile
│   └── booking_history.php # Booking history
├── index.php             # Homepage
├── events.php            # Event listings
├── event_details.php     # Event details
├── login.php             # User login
├── register.php          # User registration
├── cart.php              # Shopping cart
├── checkout.php          # Checkout process
├── database.sql          # Database schema
└── README.md             # This file
```

## Default Accounts

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Administrator

### Sample Events
The database includes sample events in various categories (Technology, Music, Food, Business) to demonstrate the platform's functionality.

## Security Features

- Password hashing using PHP's `password_hash()`
- SQL injection prevention using PDO prepared statements
- XSS protection through input sanitization
- Session management for user authentication
- Role-based access control (User/Admin)

## Customization

### Adding New Event Categories
Edit the category options in:
- `events.php` (filter dropdown)
- `admin/add_event.php` (category selection)

### Modifying Styling
- Main styles: `assets/css/style.css`
- Bootstrap customization: Override Bootstrap classes in the custom CSS

### Database Schema Modifications
- Update `database.sql` for new installations
- Create migration scripts for existing installations

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database and user exist

2. **Permission Denied Errors**
   - Check file permissions: `sudo chmod -R 755 /var/www/html/EventTopia/`
   - Ensure Apache has read access to files

3. **Images Not Loading**
   - Check image directory permissions
   - Verify image URLs are accessible
   - Ensure `assets/images/` directory exists

4. **Session Issues**
   - Check PHP session configuration
   - Ensure session directory is writable
   - Verify session cookies are enabled

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the code documentation

## Future Enhancements

- Payment gateway integration (Stripe, PayPal)
- Email notifications for bookings
- QR code generation for tickets
- Advanced reporting and analytics
- Multi-language support
- Social media integration
- Mobile app development

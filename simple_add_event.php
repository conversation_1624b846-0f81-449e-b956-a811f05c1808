<?php
require_once 'config/config.php';

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

$page_title = 'Add New Event';
$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $event_date = sanitizeInput($_POST['event_date']);
    $event_time = sanitizeInput($_POST['event_time']);
    $venue = sanitizeInput($_POST['venue']);
    $location = sanitizeInput($_POST['location']);
    $organizer = sanitizeInput($_POST['organizer']);
    $organizer_contact = sanitizeInput($_POST['organizer_contact']);
    $price = (float)$_POST['price'];
    $total_tickets = (int)$_POST['total_tickets'];
    $category = sanitizeInput($_POST['category']);
    
    // Basic validation
    if (empty($title) || empty($description) || empty($event_date) || empty($event_time) ||
        empty($venue) || empty($location) || empty($organizer) || $price < 0 || $total_tickets < 1) {
        $error = 'Please fill in all required fields with valid values';
    } elseif (strtotime($event_date) < strtotime(date('Y-m-d'))) {
        $error = 'Event date cannot be in the past';
    } else {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO events (title, description, event_date, event_time, venue, location,
                                  organizer, organizer_contact, image, price, available_tickets,
                                  total_tickets, category, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");

            $result = $stmt->execute([
                $title, $description, $event_date, $event_time, $venue, $location,
                $organizer, $organizer_contact, '', $price, $total_tickets,
                $total_tickets, $category
            ]);

            if ($result) {
                $success = 'Event created successfully! Event ID: ' . $pdo->lastInsertId();
            } else {
                $error = 'Failed to create event';
            }

        } catch (Exception $e) {
            $error = 'An error occurred while creating the event: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Event - EventTopia</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Add New Event</h1>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="mb-3">
                <label for="title" class="form-label">Event Title *</label>
                <input type="text" class="form-control" id="title" name="title" 
                       value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">Description *</label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="event_date" class="form-label">Event Date *</label>
                    <input type="date" class="form-control" id="event_date" name="event_date" 
                           value="<?php echo isset($_POST['event_date']) ? $_POST['event_date'] : ''; ?>" 
                           min="<?php echo date('Y-m-d'); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="event_time" class="form-label">Event Time *</label>
                    <input type="time" class="form-control" id="event_time" name="event_time" 
                           value="<?php echo isset($_POST['event_time']) ? $_POST['event_time'] : ''; ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="venue" class="form-label">Venue Name *</label>
                <input type="text" class="form-control" id="venue" name="venue" 
                       value="<?php echo isset($_POST['venue']) ? htmlspecialchars($_POST['venue']) : ''; ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="location" class="form-label">Full Address *</label>
                <input type="text" class="form-control" id="location" name="location" 
                       value="<?php echo isset($_POST['location']) ? htmlspecialchars($_POST['location']) : ''; ?>" required>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="organizer" class="form-label">Organizer Name *</label>
                    <input type="text" class="form-control" id="organizer" name="organizer" 
                           value="<?php echo isset($_POST['organizer']) ? htmlspecialchars($_POST['organizer']) : ''; ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="organizer_contact" class="form-label">Contact Email</label>
                    <input type="email" class="form-control" id="organizer_contact" name="organizer_contact" 
                           value="<?php echo isset($_POST['organizer_contact']) ? htmlspecialchars($_POST['organizer_contact']) : ''; ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">Ticket Price (XAF) *</label>
                    <input type="number" class="form-control" id="price" name="price" 
                           value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" 
                           min="0" step="0.01" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="total_tickets" class="form-label">Total Tickets Available *</label>
                    <input type="number" class="form-control" id="total_tickets" name="total_tickets" 
                           value="<?php echo isset($_POST['total_tickets']) ? $_POST['total_tickets'] : ''; ?>" 
                           min="1" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">Select Category</option>
                    <option value="Technology">Technology</option>
                    <option value="Music">Music</option>
                    <option value="Food">Food</option>
                    <option value="Business">Business</option>
                    <option value="Sports">Sports</option>
                    <option value="Arts">Arts</option>
                    <option value="Education">Education</option>
                </select>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Create Event</button>
                <a href="events.php" class="btn btn-outline-secondary">View Events</a>
                <a href="index.php" class="btn btn-outline-secondary">Home</a>
            </div>
        </form>
    </div>
</body>
</html>

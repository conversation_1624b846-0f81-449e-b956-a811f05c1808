<?php
// Test login functionality
require_once 'config/config.php';

$email = '<EMAIL>';
$password = 'admin123';

echo "Testing login for: $email\n";

// Check user credentials
$stmt = $pdo->prepare("SELECT id, username, email, password, first_name, last_name, role FROM users WHERE email = ?");
$stmt->execute([$email]);
$user = $stmt->fetch();

if ($user) {
    echo "User found in database:\n";
    echo "- ID: " . $user['id'] . "\n";
    echo "- Username: " . $user['username'] . "\n";
    echo "- Email: " . $user['email'] . "\n";
    echo "- Role: " . $user['role'] . "\n";
    echo "- Password hash: " . substr($user['password'], 0, 30) . "...\n";
    
    if (password_verify($password, $user['password'])) {
        echo "\n✅ PASSWORD VERIFICATION SUCCESSFUL!\n";
        echo "Login should work with these credentials:\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
    } else {
        echo "\n❌ PASSWORD VERIFICATION FAILED!\n";
        echo "The password 'admin123' does not match the stored hash.\n";
    }
} else {
    echo "❌ User not found in database!\n";
}
?>

<?php
// <PERSON><PERSON><PERSON> to fix admin password
$password = 'admin123';
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

echo "Original password: " . $password . "\n";
echo "Hashed password: " . $hashed_password . "\n";

// Database connection
$host = 'localhost';
$dbname = 'eventtopia';
$username = 'eventtopia_user';
$db_password = 'eventtopia123';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Update admin password
    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = '<EMAIL>'");
    $result = $stmt->execute([$hashed_password]);
    
    if ($result) {
        echo "Admin password updated successfully!\n";
        echo "You can now login with:\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
    } else {
        echo "Failed to update password.\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>

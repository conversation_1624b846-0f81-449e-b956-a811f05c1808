<?php
require_once 'config/config.php';

$page_title = 'Home';

// Get featured events (latest 6 events)
$stmt = $pdo->prepare("SELECT * FROM events WHERE status = 'active' AND event_date >= CURDATE() ORDER BY created_at DESC LIMIT 6");
$stmt->execute();
$featured_events = $stmt->fetchAll();

// Get event statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as total_events FROM events WHERE status = 'active'");
$stmt->execute();
$total_events = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) as total_bookings FROM bookings WHERE status = 'confirmed'");
$stmt->execute();
$total_bookings = $stmt->fetchColumn();

include 'includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="fade-in-up">Welcome to EventTopia</h1>
                <p class="lead fade-in-up">Discover amazing events, connect with like-minded people, and create unforgettable memories</p>
                <div class="fade-in-up">
                    <a href="events.php" class="btn btn-light btn-lg me-3">Browse Events</a>
                    <?php if (!isLoggedIn()): ?>
                        <a href="register.php" class="btn btn-outline-light btn-lg">Join Now</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="search-filter-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <form id="searchForm" class="d-flex gap-3">
                    <input type="text" id="searchInput" class="form-control search-box" placeholder="Search events...">
                    <select id="categoryFilter" class="form-select filter-select">
                        <option value="">All Categories</option>
                        <option value="Technology">Technology</option>
                        <option value="Music">Music</option>
                        <option value="Food">Food</option>
                        <option value="Business">Business</option>
                        <option value="Sports">Sports</option>
                        <option value="Arts">Arts</option>
                    </select>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_events; ?></div>
                    <div>Active Events</div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="stat-card bg-success">
                    <div class="stat-number"><?php echo $total_bookings; ?></div>
                    <div>Happy Customers</div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="stat-card bg-warning text-dark">
                    <div class="stat-number">24/7</div>
                    <div>Customer Support</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Featured Events</h2>
                <p class="text-muted">Don't miss out on these amazing upcoming events</p>
            </div>
        </div>
        
        <?php if (empty($featured_events)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No events available at the moment. Check back soon!
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($featured_events as $event): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card event-card">
                            <?php if ($event['image']): ?>
                                <img src="<?php echo htmlspecialchars($event['image']); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($event['title']); ?>">
                            <?php else: ?>
                                <div class="card-img-top bg-primary d-flex align-items-center justify-content-center text-white" style="height: 200px;">
                                    <i class="fas fa-calendar-alt fa-3x"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($event['title']); ?></h5>
                                <p class="event-date">
                                    <i class="fas fa-calendar me-2"></i>
                                    <?php echo formatDate($event['event_date']) . ' at ' . formatTime($event['event_time']); ?>
                                </p>
                                <p class="event-location">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    <?php echo htmlspecialchars($event['location']); ?>
                                </p>
                                <p class="card-text"><?php echo substr(htmlspecialchars($event['description']), 0, 100) . '...'; ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="event-price"><?php echo formatPrice($event['price']); ?></span>
                                    <a href="event_details.php?id=<?php echo $event['id']; ?>" class="btn btn-primary">View Details</a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="row">
                <div class="col-12 text-center mt-4">
                    <a href="events.php" class="btn btn-outline-primary btn-lg">View All Events</a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Why Choose EventTopia?</h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mb-3">
                    <i class="fas fa-search fa-3x text-primary"></i>
                </div>
                <h4>Easy Discovery</h4>
                <p>Find events that match your interests with our powerful search and filtering tools.</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mb-3">
                    <i class="fas fa-shield-alt fa-3x text-success"></i>
                </div>
                <h4>Secure Booking</h4>
                <p>Book with confidence knowing your transactions are secure and your data is protected.</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mb-3">
                    <i class="fas fa-headset fa-3x text-info"></i>
                </div>
                <h4>24/7 Support</h4>
                <p>Our dedicated support team is here to help you whenever you need assistance.</p>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

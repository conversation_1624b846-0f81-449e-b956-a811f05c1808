<?php
require_once 'config/config.php';

// Get event ID
$event_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$event_id) {
    redirect('events.php');
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND status = 'active'");
$stmt->execute([$event_id]);
$event = $stmt->fetch();

if (!$event) {
    redirect('events.php');
}

$page_title = $event['title'];

include 'includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Event Details Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Event Image -->
            <div class="col-lg-6 mb-4">
                <?php if ($event['image']): ?>
                    <img src="<?php echo htmlspecialchars($event['image']); ?>" 
                         class="img-fluid rounded shadow" 
                         alt="<?php echo htmlspecialchars($event['title']); ?>"
                         style="width: 100%; height: 400px; object-fit: cover;">
                <?php else: ?>
                    <div class="bg-primary d-flex align-items-center justify-content-center text-white rounded shadow" 
                         style="height: 400px;">
                        <i class="fas fa-calendar-alt fa-5x"></i>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Event Information -->
            <div class="col-lg-6">
                <div class="event-details">
                    <h1 class="fw-bold mb-3"><?php echo htmlspecialchars($event['title']); ?></h1>
                    
                    <?php if ($event['category']): ?>
                        <span class="badge bg-primary fs-6 mb-3"><?php echo htmlspecialchars($event['category']); ?></span>
                    <?php endif; ?>
                    
                    <div class="event-info mb-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <strong>Date:</strong><br>
                                    <span class="ms-4"><?php echo formatDate($event['event_date']); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <strong>Time:</strong><br>
                                    <span class="ms-4"><?php echo formatTime($event['event_time']); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <strong>Venue:</strong><br>
                                    <span class="ms-4"><?php echo htmlspecialchars($event['venue']); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-location-arrow text-primary me-2"></i>
                                    <strong>Location:</strong><br>
                                    <span class="ms-4"><?php echo htmlspecialchars($event['location']); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <strong>Organizer:</strong><br>
                                    <span class="ms-4"><?php echo htmlspecialchars($event['organizer']); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-ticket-alt text-primary me-2"></i>
                                    <strong>Available Tickets:</strong><br>
                                    <span class="ms-4"><?php echo $event['available_tickets']; ?> / <?php echo $event['total_tickets']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Price and Booking -->
                    <div class="booking-section bg-light p-4 rounded">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h3 class="text-success fw-bold mb-0"><?php echo formatPrice($event['price']); ?></h3>
                                <small class="text-muted">per ticket</small>
                            </div>
                            <div class="col-md-6">
                                <?php if (isLoggedIn()): ?>
                                    <?php if ($event['available_tickets'] > 0): ?>
                                        <div class="d-flex align-items-center gap-2 mb-3">
                                            <label for="quantity" class="form-label mb-0">Quantity:</label>
                                            <select class="form-select quantity-input" id="quantity" style="width: auto;">
                                                <?php for ($i = 1; $i <= min(10, $event['available_tickets']); $i++): ?>
                                                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                        <button class="btn btn-primary btn-lg w-100 add-to-cart" 
                                                data-event-id="<?php echo $event['id']; ?>">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            Add to Cart
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-secondary btn-lg w-100" disabled>
                                            <i class="fas fa-times me-2"></i>
                                            Sold Out
                                        </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <a href="login.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        Login to Book
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Event Description -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Event Description
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="lead"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Organizer Contact -->
        <?php if ($event['organizer_contact']): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                Contact Organizer
                            </h4>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">
                                <strong>Contact:</strong> 
                                <a href="mailto:<?php echo htmlspecialchars($event['organizer_contact']); ?>" 
                                   class="text-primary">
                                    <?php echo htmlspecialchars($event['organizer_contact']); ?>
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Map Section (Placeholder) -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-map me-2"></i>
                            Event Location
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-4 rounded text-center">
                            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                            <h5><?php echo htmlspecialchars($event['venue']); ?></h5>
                            <p class="text-muted"><?php echo htmlspecialchars($event['location']); ?></p>
                            <small class="text-muted">Interactive map integration can be added here</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Back to Events -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="events.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Events
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

// EventTopia Main JavaScript

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Search functionality
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // Filter functionality
    $('.filter-select').on('change', function() {
        performSearch();
    });

    // Add to cart functionality
    $('.add-to-cart').on('click', function(e) {
        e.preventDefault();
        var eventId = $(this).data('event-id');
        var quantity = $(this).closest('.event-details').find('.quantity-input').val() || 1;
        addToCart(eventId, quantity);
    });

    // Update cart quantity
    $('.quantity-btn').on('click', function() {
        var action = $(this).data('action');
        var cartId = $(this).data('cart-id');
        var currentQty = parseInt($(this).siblings('.quantity-display').text());
        var newQty = action === 'increase' ? currentQty + 1 : Math.max(1, currentQty - 1);
        updateCartQuantity(cartId, newQty);
    });

    // Remove from cart
    $('.remove-from-cart').on('click', function(e) {
        e.preventDefault();
        var cartId = $(this).data('cart-id');
        removeFromCart(cartId);
    });

    // Form validation
    $('form').on('submit', function() {
        var isValid = true;
        $(this).find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    });

    // Password confirmation validation
    $('#confirmPassword').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();
        
        if (password !== confirmPassword) {
            $(this).addClass('is-invalid');
            $('#passwordError').text('Passwords do not match');
        } else {
            $(this).removeClass('is-invalid');
            $('#passwordError').text('');
        }
    });

    // Auto-hide alerts
    $('.alert').delay(5000).fadeOut();
});

// Search function
function performSearch() {
    var searchTerm = $('#searchInput').val();
    var category = $('#categoryFilter').val();
    var location = $('#locationFilter').val();
    var dateFilter = $('#dateFilter').val();

    var params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (category) params.append('category', category);
    if (location) params.append('location', location);
    if (dateFilter) params.append('date', dateFilter);

    window.location.href = 'events.php?' + params.toString();
}

// Add to cart function
function addToCart(eventId, quantity) {
    $.ajax({
        url: 'ajax/add_to_cart.php',
        method: 'POST',
        data: {
            event_id: eventId,
            quantity: quantity
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Event added to cart successfully!');
                updateCartCount();
            } else {
                showAlert('danger', response.message || 'Failed to add event to cart');
            }
        },
        error: function() {
            showAlert('danger', 'An error occurred. Please try again.');
        }
    });
}

// Update cart quantity
function updateCartQuantity(cartId, quantity) {
    $.ajax({
        url: 'ajax/update_cart.php',
        method: 'POST',
        data: {
            cart_id: cartId,
            quantity: quantity
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                showAlert('danger', response.message || 'Failed to update cart');
            }
        },
        error: function() {
            showAlert('danger', 'An error occurred. Please try again.');
        }
    });
}

// Remove from cart
function removeFromCart(cartId) {
    if (confirm('Are you sure you want to remove this item from your cart?')) {
        $.ajax({
            url: 'ajax/remove_from_cart.php',
            method: 'POST',
            data: {
                cart_id: cartId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    showAlert('danger', response.message || 'Failed to remove item from cart');
                }
            },
            error: function() {
                showAlert('danger', 'An error occurred. Please try again.');
            }
        });
    }
}

// Update cart count in navbar
function updateCartCount() {
    $.ajax({
        url: 'ajax/get_cart_count.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.count > 0) {
                $('.navbar .badge').text(response.count).show();
            } else {
                $('.navbar .badge').hide();
            }
        }
    });
}

// Show alert messages
function showAlert(type, message) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>';
    
    $('#alertContainer').html(alertHtml);
    $('.alert').delay(5000).fadeOut();
}

// Format currency
function formatCurrency(amount) {
    return '$' + parseFloat(amount).toFixed(2);
}

// Validate email format
function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate phone format
function isValidPhone(phone) {
    var phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

/* EventTopia Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Event Cards */
.event-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-card img {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.event-card .card-body {
    padding: 1.5rem;
}

.event-card .card-title {
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.event-card .event-date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.event-card .event-location {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.event-card .event-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Search and Filter Section */
.search-filter-section {
    background-color: var(--light-color);
    padding: 2rem 0;
    border-bottom: 1px solid #dee2e6;
}

.search-box {
    border-radius: 25px;
    border: 2px solid var(--primary-color);
    padding: 0.75rem 1.5rem;
}

.search-box:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    border-color: var(--primary-color);
}

.filter-select {
    border-radius: 10px;
    border: 2px solid #dee2e6;
}

/* Buttons */
.btn-primary {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border: none;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Auth Pages */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.auth-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 2rem;
    text-align: center;
}

/* Cart and Checkout */
.cart-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* Dashboard */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Admin Panel */
.admin-sidebar {
    background-color: var(--dark-color);
    min-height: 100vh;
    padding: 2rem 0;
}

.admin-sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .event-card img {
        height: 150px;
    }
    
    .btn-primary,
    .btn-outline-primary {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

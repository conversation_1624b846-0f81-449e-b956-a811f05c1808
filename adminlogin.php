<?php
require_once 'config/config.php';

// Redirect if already logged in as admin
if (isLoggedIn() && isAdmin()) {
    redirect('admin/index.php');
}

// Redirect regular users to main login
if (isLoggedIn() && !isAdmin()) {
    redirect('login.php');
}

$page_title = 'Admin Login';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        // Check admin credentials only
        $stmt = $pdo->prepare("SELECT id, username, email, password, first_name, last_name, role FROM users WHERE email = ? AND role = 'admin'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['role'] = $user['role'];
            
            redirect('admin/index.php');
        } else {
            $error = 'Invalid admin credentials';
        }
    }
}

include 'includes/header.php';
?>

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card auth-card">
                    <div class="auth-header bg-dark">
                        <h3 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Admin Login
                        </h3>
                        <p class="mb-0 mt-2">Access the EventTopia Administration Panel</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="email" class="form-label">Admin Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-dark w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Access Admin Panel
                            </button>
                        </form>
                        
                        <div class="text-center">
                            <p class="mb-0">
                                <a href="index.php" class="text-primary text-decoration-none">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to Main Site
                                </a>
                            </p>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                This login is restricted to administrators only
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

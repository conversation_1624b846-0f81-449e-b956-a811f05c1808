<?php
require_once 'config/config.php';

$page_title = 'Events';

// Get search and filter parameters
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
$location = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$date_filter = isset($_GET['date']) ? sanitizeInput($_GET['date']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * EVENTS_PER_PAGE;

// Build query
$where_conditions = ["status = 'active'", "event_date >= CURDATE()"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ? OR organizer LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($category)) {
    $where_conditions[] = "category = ?";
    $params[] = $category;
}

if (!empty($location)) {
    $where_conditions[] = "location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($date_filter)) {
    switch ($date_filter) {
        case 'today':
            $where_conditions[] = "event_date = CURDATE()";
            break;
        case 'tomorrow':
            $where_conditions[] = "event_date = DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
            break;
        case 'this_week':
            $where_conditions[] = "event_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
            break;
        case 'this_month':
            $where_conditions[] = "MONTH(event_date) = MONTH(CURDATE()) AND YEAR(event_date) = YEAR(CURDATE())";
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM events WHERE $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_events = $count_stmt->fetchColumn();
$total_pages = ceil($total_events / EVENTS_PER_PAGE);

// Get events
$sql = "SELECT * FROM events WHERE $where_clause ORDER BY event_date ASC, event_time ASC LIMIT ? OFFSET ?";
$params[] = EVENTS_PER_PAGE;
$params[] = $offset;
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$events = $stmt->fetchAll();

// Get unique categories and locations for filters
$categories_stmt = $pdo->prepare("SELECT DISTINCT category FROM events WHERE status = 'active' AND category IS NOT NULL ORDER BY category");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);

$locations_stmt = $pdo->prepare("SELECT DISTINCT location FROM events WHERE status = 'active' ORDER BY location");
$locations_stmt->execute();
$locations = $locations_stmt->fetchAll(PDO::FETCH_COLUMN);

include 'includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="fw-bold">Discover Amazing Events</h1>
                <p class="lead">Find the perfect event for you from our extensive collection</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<section class="search-filter-section">
    <div class="container">
        <form id="searchForm" method="GET" action="">
            <div class="row g-3">
                <div class="col-md-4">
                    <input type="text" id="searchInput" name="search" class="form-control search-box" 
                           placeholder="Search events..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <select id="categoryFilter" name="category" class="form-select filter-select">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat); ?>" 
                                    <?php echo $category === $cat ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select id="locationFilter" name="location" class="form-select filter-select">
                        <option value="">All Locations</option>
                        <?php foreach ($locations as $loc): ?>
                            <option value="<?php echo htmlspecialchars($loc); ?>" 
                                    <?php echo $location === $loc ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($loc); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select id="dateFilter" name="date" class="form-select filter-select">
                        <option value="">Any Date</option>
                        <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>>Today</option>
                        <option value="tomorrow" <?php echo $date_filter === 'tomorrow' ? 'selected' : ''; ?>>Tomorrow</option>
                        <option value="this_week" <?php echo $date_filter === 'this_week' ? 'selected' : ''; ?>>This Week</option>
                        <option value="this_month" <?php echo $date_filter === 'this_month' ? 'selected' : ''; ?>>This Month</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Events Section -->
<section class="py-5">
    <div class="container">
        <!-- Results Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h4>
                        <?php if ($total_events > 0): ?>
                            Showing <?php echo $offset + 1; ?>-<?php echo min($offset + EVENTS_PER_PAGE, $total_events); ?> 
                            of <?php echo $total_events; ?> events
                        <?php else: ?>
                            No events found
                        <?php endif; ?>
                    </h4>
                    <?php if (!empty($search) || !empty($category) || !empty($location) || !empty($date_filter)): ?>
                        <a href="events.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <?php if (empty($events)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No events found matching your criteria. Try adjusting your search filters.
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Events Grid -->
            <div class="row">
                <?php foreach ($events as $event): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card event-card">
                            <?php if ($event['image']): ?>
                                <img src="<?php echo htmlspecialchars($event['image']); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($event['title']); ?>">
                            <?php else: ?>
                                <div class="card-img-top bg-primary d-flex align-items-center justify-content-center text-white" style="height: 200px;">
                                    <i class="fas fa-calendar-alt fa-3x"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($event['title']); ?></h5>
                                <p class="event-date">
                                    <i class="fas fa-calendar me-2"></i>
                                    <?php echo formatDate($event['event_date']) . ' at ' . formatTime($event['event_time']); ?>
                                </p>
                                <p class="event-location">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    <?php echo htmlspecialchars($event['location']); ?>
                                </p>
                                <p class="card-text"><?php echo substr(htmlspecialchars($event['description']), 0, 100) . '...'; ?></p>
                                
                                <?php if ($event['category']): ?>
                                    <span class="badge bg-secondary mb-2"><?php echo htmlspecialchars($event['category']); ?></span>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="event-price"><?php echo formatPrice($event['price']); ?></span>
                                    <div>
                                        <small class="text-muted"><?php echo $event['available_tickets']; ?> tickets left</small>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <a href="event_details.php?id=<?php echo $event['id']; ?>" class="btn btn-primary w-100">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="Events pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

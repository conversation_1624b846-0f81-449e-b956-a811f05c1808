<?php
require_once 'config/config.php';

// Require login
requireLogin();

$page_title = 'Checkout';

// Get cart items
$stmt = $pdo->prepare("
    SELECT c.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.price, e.available_tickets
    FROM cart c
    JOIN events e ON c.event_id = e.id
    WHERE c.user_id = ? AND e.status = 'active'
    ORDER BY c.added_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$cart_items = $stmt->fetchAll();

// Redirect if cart is empty
if (empty($cart_items)) {
    redirect('cart.php');
}

// Calculate totals
$subtotal = 0;
foreach ($cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}

$tax_rate = 0.08; // 8% tax
$tax_amount = $subtotal * $tax_rate;
$total = $subtotal + $tax_amount;

$error = '';
$success = '';

// Process checkout
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $attendee_name = sanitizeInput($_POST['attendee_name']);
    $attendee_email = sanitizeInput($_POST['attendee_email']);
    $attendee_phone = sanitizeInput($_POST['attendee_phone']);
    $payment_method = sanitizeInput($_POST['payment_method']);
    
    // Basic validation
    if (empty($attendee_name) || empty($attendee_email) || empty($payment_method)) {
        $error = 'Please fill in all required fields';
    } else {
        try {
            $pdo->beginTransaction();
            
            // Process each cart item
            foreach ($cart_items as $item) {
                // Check if tickets are still available
                $stmt = $pdo->prepare("SELECT available_tickets FROM events WHERE id = ? FOR UPDATE");
                $stmt->execute([$item['event_id']]);
                $current_available = $stmt->fetchColumn();
                
                if ($current_available < $item['quantity']) {
                    throw new Exception("Not enough tickets available for " . $item['title']);
                }
                
                // Create booking
                $booking_reference = generateBookingReference();
                $booking_total = $item['price'] * $item['quantity'];
                
                $stmt = $pdo->prepare("
                    INSERT INTO bookings (user_id, event_id, quantity, total_amount, booking_reference, 
                                        attendee_name, attendee_email, attendee_phone, status, payment_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'confirmed', 'completed')
                ");
                $stmt->execute([
                    $_SESSION['user_id'],
                    $item['event_id'],
                    $item['quantity'],
                    $booking_total,
                    $booking_reference,
                    $attendee_name,
                    $attendee_email,
                    $attendee_phone
                ]);
                
                // Update available tickets
                $stmt = $pdo->prepare("UPDATE events SET available_tickets = available_tickets - ? WHERE id = ?");
                $stmt->execute([$item['quantity'], $item['event_id']]);
            }
            
            // Clear cart
            $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            
            $pdo->commit();
            
            // Redirect to success page
            $_SESSION['checkout_success'] = true;
            redirect('user/booking_history.php');
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Checkout
                </h1>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Checkout Form -->
            <div class="col-lg-8">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <!-- Attendee Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                Attendee Information
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="attendee_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="attendee_name" name="attendee_name" 
                                           value="<?php echo isset($_POST['attendee_name']) ? htmlspecialchars($_POST['attendee_name']) : $_SESSION['first_name'] . ' ' . $_SESSION['last_name']; ?>" 
                                           required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="attendee_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="attendee_email" name="attendee_email" 
                                           value="<?php echo isset($_POST['attendee_email']) ? htmlspecialchars($_POST['attendee_email']) : $_SESSION['email']; ?>" 
                                           required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="attendee_phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="attendee_phone" name="attendee_phone" 
                                           value="<?php echo isset($_POST['attendee_phone']) ? htmlspecialchars($_POST['attendee_phone']) : ''; ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Information
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Payment Method *</label>
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="credit_card" value="credit_card" checked>
                                            <label class="form-check-label" for="credit_card">
                                                <i class="fas fa-credit-card me-2"></i>Credit Card
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal">
                                            <label class="form-check-label" for="paypal">
                                                <i class="fab fa-paypal me-2"></i>PayPal
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                                            <label class="form-check-label" for="bank_transfer">
                                                <i class="fas fa-university me-2"></i>Bank Transfer
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Credit Card Fields (Demo) -->
                            <div id="credit_card_fields">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="card_number" class="form-label">Card Number</label>
                                        <input type="text" class="form-control" id="card_number" placeholder="1234 5678 9012 3456">
                                        <small class="text-muted">Demo: Use any 16-digit number</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="card_name" class="form-label">Cardholder Name</label>
                                        <input type="text" class="form-control" id="card_name" placeholder="John Doe">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="expiry_month" class="form-label">Expiry Month</label>
                                        <select class="form-select" id="expiry_month">
                                            <option value="">Month</option>
                                            <?php for ($i = 1; $i <= 12; $i++): ?>
                                                <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="expiry_year" class="form-label">Expiry Year</label>
                                        <select class="form-select" id="expiry_year">
                                            <option value="">Year</option>
                                            <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                                <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="cvv" class="form-label">CVV</label>
                                        <input type="text" class="form-control" id="cvv" placeholder="123">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Demo Mode:</strong> This is a demonstration checkout. No real payment will be processed.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and 
                                    <a href="#" class="text-primary">Refund Policy</a> *
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success btn-lg w-100">
                        <i class="fas fa-lock me-2"></i>
                        Complete Purchase - <?php echo formatPrice($total); ?>
                    </button>
                </form>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Order Summary</h4>
                    </div>
                    <div class="card-body">
                        <!-- Cart Items -->
                        <?php foreach ($cart_items as $item): ?>
                            <div class="d-flex justify-content-between align-items-start mb-3 pb-3 border-bottom">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($item['title']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo formatDate($item['event_date']); ?><br>
                                        Qty: <?php echo $item['quantity']; ?> × <?php echo formatPrice($item['price']); ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <strong><?php echo formatPrice($item['price'] * $item['quantity']); ?></strong>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <!-- Totals -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span><?php echo formatPrice($subtotal); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (8%):</span>
                            <span><?php echo formatPrice($tax_amount); ?></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong class="text-success"><?php echo formatPrice($total); ?></strong>
                        </div>
                    </div>
                </div>
                
                <!-- Security Features -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                                <small class="d-block">Secure</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-lock text-success fa-2x mb-2"></i>
                                <small class="d-block">Encrypted</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-certificate text-success fa-2x mb-2"></i>
                                <small class="d-block">Verified</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

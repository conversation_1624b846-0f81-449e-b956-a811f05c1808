<?php
// Test simplified admin add event
require_once 'config/config.php';

// Simulate admin login
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

echo "<h2>Simplified Admin Add Event Test</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $event_date = sanitizeInput($_POST['event_date']);
    $event_time = sanitizeInput($_POST['event_time']);
    $venue = sanitizeInput($_POST['venue']);
    $location = sanitizeInput($_POST['location']);
    $organizer = sanitizeInput($_POST['organizer']);
    $organizer_contact = sanitizeInput($_POST['organizer_contact']);
    $price = (float)$_POST['price'];
    $total_tickets = (int)$_POST['total_tickets'];
    $category = sanitizeInput($_POST['category']);
    $image_url = sanitizeInput($_POST['image_url']);

    // Basic validation
    if (empty($title) || empty($description) || empty($event_date) || empty($event_time) ||
        empty($venue) || empty($location) || empty($organizer) || $price < 0 || $total_tickets < 1) {
        $error = 'Please fill in all required fields with valid values';
    } elseif (strtotime($event_date) < strtotime(date('Y-m-d'))) {
        $error = 'Event date cannot be in the past';
    } else {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO events (title, description, event_date, event_time, venue, location,
                                  organizer, organizer_contact, image, price, available_tickets,
                                  total_tickets, category, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");

            $result = $stmt->execute([
                $title, $description, $event_date, $event_time, $venue, $location,
                $organizer, $organizer_contact, $image_url, $price, $total_tickets,
                $total_tickets, $category
            ]);

            if ($result) {
                $success = 'Event created successfully! Event ID: ' . $pdo->lastInsertId();
                echo "<div class='alert alert-success'>$success</div>";
            } else {
                $error = 'Failed to create event';
                echo "<div class='alert alert-danger'>$error</div>";
            }

        } catch (Exception $e) {
            $error = 'An error occurred while creating the event: ' . $e->getMessage();
            echo "<div class='alert alert-danger'>$error</div>";
        }
    }
    
    if (isset($error)) {
        echo "<div class='alert alert-danger'>$error</div>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Add Event</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <form method="POST">
            <div class="mb-3">
                <label>Event Title *</label>
                <input type="text" class="form-control" name="title" value="Admin Test Event" required>
            </div>
            <div class="mb-3">
                <label>Description *</label>
                <textarea class="form-control" name="description" required>This is a test event from admin panel</textarea>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label>Event Date *</label>
                    <input type="date" class="form-control" name="event_date" value="<?php echo date('Y-m-d', strtotime('+7 days')); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label>Event Time *</label>
                    <input type="time" class="form-control" name="event_time" value="19:00" required>
                </div>
            </div>
            <div class="mb-3">
                <label>Venue *</label>
                <input type="text" class="form-control" name="venue" value="Admin Test Venue" required>
            </div>
            <div class="mb-3">
                <label>Location *</label>
                <input type="text" class="form-control" name="location" value="Yaoundé, Cameroon" required>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label>Organizer *</label>
                    <input type="text" class="form-control" name="organizer" value="Admin Organizer" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label>Contact Email</label>
                    <input type="email" class="form-control" name="organizer_contact" value="<EMAIL>">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label>Price (XAF) *</label>
                    <input type="number" class="form-control" name="price" value="10000" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label>Total Tickets *</label>
                    <input type="number" class="form-control" name="total_tickets" value="150" required>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label>Category</label>
                    <select class="form-select" name="category">
                        <option value="Technology">Technology</option>
                        <option value="Music">Music</option>
                        <option value="Business">Business</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label>Image URL</label>
                    <input type="url" class="form-control" name="image_url" placeholder="https://example.com/image.jpg">
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Create Event</button>
            <a href="events.php" class="btn btn-outline-secondary">View Events</a>
        </form>
    </div>
</body>
</html>

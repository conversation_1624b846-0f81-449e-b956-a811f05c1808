<?php
// Test database connection
echo "<h2>EventTopia Database Connection Test</h2>";

try {
    require_once 'config/config.php';
    
    echo "<p>✅ Config file loaded successfully</p>";
    
    if (isset($pdo) && $pdo instanceof PDO) {
        echo "<p>✅ PDO connection established</p>";
        
        // Test query
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users");
        $stmt->execute();
        $result = $stmt->fetch();
        
        echo "<p>✅ Database query successful</p>";
        echo "<p>📊 Total users in database: " . $result['count'] . "</p>";
        
        // Test events
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM events");
        $stmt->execute();
        $result = $stmt->fetch();
        
        echo "<p>📊 Total events in database: " . $result['count'] . "</p>";
        
        // Test admin user
        $stmt = $pdo->prepare("SELECT username, email, role FROM users WHERE role = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ Admin user found: " . $admin['email'] . "</p>";
        } else {
            echo "<p>❌ No admin user found</p>";
        }
        
        echo "<h3>🎉 All tests passed! The application should be working correctly.</h3>";
        
    } else {
        echo "<p>❌ PDO connection failed</p>";
        echo "<p>PDO object: " . var_export($pdo, true) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to EventTopia</a></p>";
echo "<p><a href='adminlogin.php'>Admin Login</a></p>";
?>

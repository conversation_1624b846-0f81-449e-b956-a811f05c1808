<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Manage Events';

// Handle event status updates
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $event_id = (int)$_POST['event_id'];
    
    if ($_POST['action'] === 'toggle_status') {
        $new_status = $_POST['new_status'];
        $stmt = $pdo->prepare("UPDATE events SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $event_id]);
        
        $_SESSION['admin_message'] = 'Event status updated successfully';
    } elseif ($_POST['action'] === 'delete') {
        // Check if event has bookings
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE event_id = ?");
        $stmt->execute([$event_id]);
        $booking_count = $stmt->fetchColumn();
        
        if ($booking_count > 0) {
            $_SESSION['admin_error'] = 'Cannot delete event with existing bookings';
        } else {
            $stmt = $pdo->prepare("DELETE FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $_SESSION['admin_message'] = 'Event deleted successfully';
        }
    }
    
    redirect('events.php');
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * EVENTS_PER_PAGE;

// Get total events count
$stmt = $pdo->prepare("SELECT COUNT(*) FROM events");
$stmt->execute();
$total_events = $stmt->fetchColumn();
$total_pages = ceil($total_events / EVENTS_PER_PAGE);

// Get events
$stmt = $pdo->prepare("
    SELECT e.*, 
           COUNT(b.id) as booking_count,
           SUM(CASE WHEN b.status = 'confirmed' THEN b.total_amount ELSE 0 END) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id
    GROUP BY e.id
    ORDER BY e.created_at DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([EVENTS_PER_PAGE, $offset]);
$events = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<?php if (isset($_SESSION['admin_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_SESSION['admin_message']; unset($_SESSION['admin_message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['admin_error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_SESSION['admin_error']; unset($_SESSION['admin_error']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Manage Events
                </h1>
                <p class="mb-0 mt-2">Create, edit, and manage all events</p>
            </div>
        </div>
    </div>
</div>

<!-- Events Management Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Action Bar -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4>All Events (<?php echo $total_events; ?>)</h4>
                <a href="add_event.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add New Event
                </a>
            </div>
            
            <!-- Events Table -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($events)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-5x text-muted mb-3"></i>
                            <h4>No events found</h4>
                            <p class="text-muted">Start by creating your first event</p>
                            <a href="add_event.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Add New Event
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date & Time</th>
                                        <th>Location</th>
                                        <th>Price</th>
                                        <th>Tickets</th>
                                        <th>Bookings</th>
                                        <th>Revenue</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($events as $event): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($event['image']): ?>
                                                        <img src="../<?php echo htmlspecialchars($event['image']); ?>" 
                                                             class="rounded me-3" 
                                                             style="width: 50px; height: 50px; object-fit: cover;"
                                                             alt="<?php echo htmlspecialchars($event['title']); ?>">
                                                    <?php else: ?>
                                                        <div class="bg-primary d-flex align-items-center justify-content-center text-white rounded me-3" 
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-calendar-alt"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($event['title']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($event['organizer']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo formatDate($event['event_date']); ?><br>
                                                <small class="text-muted"><?php echo formatTime($event['event_time']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($event['venue']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($event['location']); ?></small>
                                            </td>
                                            <td><?php echo formatPrice($event['price']); ?></td>
                                            <td>
                                                <?php echo $event['available_tickets']; ?> / <?php echo $event['total_tickets']; ?>
                                                <?php if ($event['available_tickets'] == 0): ?>
                                                    <br><span class="badge bg-danger">Sold Out</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $event['booking_count']; ?></td>
                                            <td><?php echo formatPrice($event['revenue']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $event['status'] === 'active' ? 'success' : ($event['status'] === 'inactive' ? 'warning' : 'danger'); ?>">
                                                    <?php echo ucfirst($event['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="../event_details.php?id=<?php echo $event['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       target="_blank" 
                                                       title="View Event">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_event.php?id=<?php echo $event['id']; ?>" 
                                                       class="btn btn-sm btn-outline-warning" 
                                                       title="Edit Event">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-<?php echo $event['status'] === 'active' ? 'secondary' : 'success'; ?>" 
                                                            onclick="toggleEventStatus(<?php echo $event['id']; ?>, '<?php echo $event['status'] === 'active' ? 'inactive' : 'active'; ?>')"
                                                            title="<?php echo $event['status'] === 'active' ? 'Deactivate' : 'Activate'; ?> Event">
                                                        <i class="fas fa-<?php echo $event['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteEvent(<?php echo $event['id']; ?>, '<?php echo htmlspecialchars($event['title']); ?>')"
                                                            title="Delete Event">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Events pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                                <i class="fas fa-chevron-left"></i> Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                                Next <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Forms for Actions -->
<form id="statusForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_status">
    <input type="hidden" name="event_id" id="statusEventId">
    <input type="hidden" name="new_status" id="newStatus">
</form>

<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="event_id" id="deleteEventId">
</form>

<script>
function toggleEventStatus(eventId, newStatus) {
    const action = newStatus === 'active' ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this event?`)) {
        document.getElementById('statusEventId').value = eventId;
        document.getElementById('newStatus').value = newStatus;
        document.getElementById('statusForm').submit();
    }
}

function deleteEvent(eventId, eventTitle) {
    if (confirm(`Are you sure you want to delete "${eventTitle}"? This action cannot be undone.`)) {
        document.getElementById('deleteEventId').value = eventId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php include '../includes/footer.php'; ?>

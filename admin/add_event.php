<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Add New Event';

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $event_date = sanitizeInput($_POST['event_date']);
    $event_time = sanitizeInput($_POST['event_time']);
    $venue = sanitizeInput($_POST['venue']);
    $location = sanitizeInput($_POST['location']);
    $organizer = sanitizeInput($_POST['organizer']);
    $organizer_contact = sanitizeInput($_POST['organizer_contact']);
    $price = (float)$_POST['price'];
    $total_tickets = (int)$_POST['total_tickets'];
    $category = sanitizeInput($_POST['category']);
    $image_url = sanitizeInput($_POST['image_url']);

    // Handle image upload
    $uploaded_image = '';
    if (isset($_FILES['event_image']) && $_FILES['event_image']['error'] == 0) {
        $upload_dir = '../assets/images/events/';
        $file_extension = strtolower(pathinfo($_FILES['event_image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($file_extension, $allowed_extensions)) {
            if ($_FILES['event_image']['size'] <= MAX_FILE_SIZE) {
                $filename = uniqid('event_') . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;

                if (move_uploaded_file($_FILES['event_image']['tmp_name'], $upload_path)) {
                    $uploaded_image = 'assets/images/events/' . $filename;
                } else {
                    $error = 'Failed to upload image';
                }
            } else {
                $error = 'Image file is too large. Maximum size is 5MB';
            }
        } else {
            $error = 'Invalid image format. Only JPG, JPEG, PNG, and GIF are allowed';
        }
    }

    // Use uploaded image if available, otherwise use URL
    $final_image = !empty($uploaded_image) ? $uploaded_image : $image_url;
    
    // Basic validation
    if (!isset($error) && (empty($title) || empty($description) || empty($event_date) || empty($event_time) ||
        empty($venue) || empty($location) || empty($organizer) || $price < 0 || $total_tickets < 1)) {
        $error = 'Please fill in all required fields with valid values';
    } elseif (!isset($error) && strtotime($event_date) < strtotime(date('Y-m-d'))) {
        $error = 'Event date cannot be in the past';
    } elseif (!isset($error)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO events (title, description, event_date, event_time, venue, location,
                                  organizer, organizer_contact, image, price, available_tickets,
                                  total_tickets, category, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");

            $result = $stmt->execute([
                $title, $description, $event_date, $event_time, $venue, $location,
                $organizer, $organizer_contact, $final_image, $price, $total_tickets,
                $total_tickets, $category
            ]);

            if ($result) {
                $_SESSION['admin_message'] = 'Event created successfully!';
                redirect('events.php');
            } else {
                $error = 'Failed to insert event into database';
            }

        } catch (Exception $e) {
            $error = 'An error occurred while creating the event: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-plus me-2"></i>
                    Add New Event
                </h1>
                <p class="mb-0 mt-2">Create a new event for your platform</p>
            </div>
        </div>
    </div>
</div>

<!-- Add Event Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-edit me-2"></i>
                                Event Information
                            </h4>
                        </div>
                        <div class="card-body">
                            <?php if ($error): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?php echo $error; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="" enctype="multipart/form-data">
                                <!-- Basic Information -->
                                <h5 class="mb-3">Basic Information</h5>
                                <div class="mb-3">
                                    <label for="title" class="form-label">Event Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" 
                                           required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="">Select Category</option>
                                            <option value="Technology" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Technology') ? 'selected' : ''; ?>>Technology</option>
                                            <option value="Music" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Music') ? 'selected' : ''; ?>>Music</option>
                                            <option value="Food" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Food') ? 'selected' : ''; ?>>Food</option>
                                            <option value="Business" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Business') ? 'selected' : ''; ?>>Business</option>
                                            <option value="Sports" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Sports') ? 'selected' : ''; ?>>Sports</option>
                                            <option value="Arts" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Arts') ? 'selected' : ''; ?>>Arts</option>
                                            <option value="Education" <?php echo (isset($_POST['category']) && $_POST['category'] === 'Education') ? 'selected' : ''; ?>>Education</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="event_image" class="form-label">Upload Image</label>
                                        <input type="file" class="form-control" id="event_image" name="event_image"
                                               accept="image/*">
                                        <small class="text-muted">Max size: 5MB. Formats: JPG, PNG, GIF</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="image_url" class="form-label">Or Image URL</label>
                                        <input type="url" class="form-control" id="image_url" name="image_url"
                                               value="<?php echo isset($_POST['image_url']) ? htmlspecialchars($_POST['image_url']) : ''; ?>"
                                               placeholder="https://example.com/image.jpg">
                                        <small class="text-muted">If both upload and URL are provided, uploaded image will be used</small>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- Date and Time -->
                                <h5 class="mb-3">Date and Time</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="event_date" class="form-label">Event Date *</label>
                                        <input type="date" class="form-control" id="event_date" name="event_date" 
                                               value="<?php echo isset($_POST['event_date']) ? $_POST['event_date'] : ''; ?>" 
                                               min="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="event_time" class="form-label">Event Time *</label>
                                        <input type="time" class="form-control" id="event_time" name="event_time" 
                                               value="<?php echo isset($_POST['event_time']) ? $_POST['event_time'] : ''; ?>" 
                                               required>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- Location -->
                                <h5 class="mb-3">Location</h5>
                                <div class="mb-3">
                                    <label for="venue" class="form-label">Venue Name *</label>
                                    <input type="text" class="form-control" id="venue" name="venue" 
                                           value="<?php echo isset($_POST['venue']) ? htmlspecialchars($_POST['venue']) : ''; ?>" 
                                           required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="location" class="form-label">Full Address *</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           value="<?php echo isset($_POST['location']) ? htmlspecialchars($_POST['location']) : ''; ?>" 
                                           placeholder="City, State, Country" required>
                                </div>
                                
                                <hr>
                                
                                <!-- Organizer -->
                                <h5 class="mb-3">Organizer Information</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="organizer" class="form-label">Organizer Name *</label>
                                        <input type="text" class="form-control" id="organizer" name="organizer" 
                                               value="<?php echo isset($_POST['organizer']) ? htmlspecialchars($_POST['organizer']) : ''; ?>" 
                                               required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="organizer_contact" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="organizer_contact" name="organizer_contact" 
                                               value="<?php echo isset($_POST['organizer_contact']) ? htmlspecialchars($_POST['organizer_contact']) : ''; ?>">
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- Pricing and Tickets -->
                                <h5 class="mb-3">Pricing and Tickets</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="price" class="form-label">Ticket Price (XAF) *</label>
                                        <input type="number" class="form-control" id="price" name="price" 
                                               value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" 
                                               min="0" step="0.01" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="total_tickets" class="form-label">Total Tickets Available *</label>
                                        <input type="number" class="form-control" id="total_tickets" name="total_tickets" 
                                               value="<?php echo isset($_POST['total_tickets']) ? $_POST['total_tickets'] : ''; ?>" 
                                               min="1" required>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Create Event
                                    </button>
                                    <a href="events.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Back to Events
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Preview/Help -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Tips
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>Creating a Great Event</h6>
                            <ul class="small">
                                <li>Use a clear, descriptive title</li>
                                <li>Provide detailed description</li>
                                <li>Add high-quality images</li>
                                <li>Set competitive pricing</li>
                                <li>Include complete location details</li>
                                <li>Provide organizer contact information</li>
                            </ul>
                            
                            <h6 class="mt-3">Image Guidelines</h6>
                            <ul class="small">
                                <li>Use high-resolution images (min 800x600)</li>
                                <li>Ensure images are relevant to the event</li>
                                <li>Use publicly accessible URLs</li>
                                <li>Consider using free stock photo sites</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

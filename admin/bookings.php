<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Manage Bookings';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * BOOKINGS_PER_PAGE;

// Get total bookings count
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings");
$stmt->execute();
$total_bookings = $stmt->fetchColumn();
$total_pages = ceil($total_bookings / BOOKINGS_PER_PAGE);

// Get bookings with event and user details
$stmt = $pdo->prepare("
    SELECT b.*, e.title as event_title, e.event_date, e.event_time, e.venue,
           u.first_name, u.last_name, u.email
    FROM bookings b
    JOIN events e ON b.event_id = e.id
    JOIN users u ON b.user_id = u.id
    ORDER BY b.booking_date DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([BOOKINGS_PER_PAGE, $offset]);
$bookings = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-ticket-alt me-2"></i>
                    Manage Bookings
                </h1>
                <p class="mb-0 mt-2">View and manage all event bookings</p>
            </div>
        </div>
    </div>
</div>

<!-- Bookings Management Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link active" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <h4 class="mb-4">All Bookings (<?php echo $total_bookings; ?>)</h4>
            
            <!-- Bookings Table -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($bookings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-ticket-alt fa-5x text-muted mb-3"></i>
                            <h4>No bookings found</h4>
                            <p class="text-muted">Bookings will appear here once users start booking events</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Customer</th>
                                        <th>Event</th>
                                        <th>Event Date</th>
                                        <th>Quantity</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Booking Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($bookings as $booking): ?>
                                        <tr>
                                            <td>
                                                <strong>#<?php echo htmlspecialchars($booking['booking_reference']); ?></strong>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($booking['email']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($booking['event_title']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($booking['venue']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo formatDate($booking['event_date']); ?><br>
                                                <small class="text-muted"><?php echo formatTime($booking['event_time']); ?></small>
                                            </td>
                                            <td><?php echo $booking['quantity']; ?></td>
                                            <td><?php echo formatPrice($booking['total_amount']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $booking['status'] === 'confirmed' ? 'success' : ($booking['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo ucfirst($booking['status']); ?>
                                                </span><br>
                                                <small class="badge bg-<?php echo $booking['payment_status'] === 'completed' ? 'success' : ($booking['payment_status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo ucfirst($booking['payment_status']); ?>
                                                </small>
                                            </td>
                                            <td><?php echo date('M j, Y g:i A', strtotime($booking['booking_date'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewBookingDetails(<?php echo $booking['id']; ?>)"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" 
                                                            onclick="generateTicket(<?php echo $booking['id']; ?>)"
                                                            title="Generate Ticket">
                                                        <i class="fas fa-ticket-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Bookings pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                                <i class="fas fa-chevron-left"></i> Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                                Next <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewBookingDetails(bookingId) {
    alert('Booking details functionality would be implemented here for booking ID: ' + bookingId);
}

function generateTicket(bookingId) {
    alert('Ticket generation functionality would be implemented here for booking ID: ' + bookingId);
}
</script>

<?php include '../includes/footer.php'; ?>

<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Manage Users';

// Get users
$stmt = $pdo->prepare("
    SELECT u.*, 
           COUNT(b.id) as total_bookings,
           SUM(CASE WHEN b.payment_status = 'completed' THEN b.total_amount ELSE 0 END) as total_spent
    FROM users u
    LEFT JOIN bookings b ON u.id = b.user_id
    WHERE u.role = 'user'
    GROUP BY u.id
    ORDER BY u.created_at DESC
");
$stmt->execute();
$users = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-users me-2"></i>
                    Manage Users
                </h1>
                <p class="mb-0 mt-2">View and manage registered users</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Management Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <h4 class="mb-4">Registered Users (<?php echo count($users); ?>)</h4>
            
            <!-- Users Table -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-5x text-muted mb-3"></i>
                            <h4>No users found</h4>
                            <p class="text-muted">Registered users will appear here</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Contact</th>
                                        <th>Registration Date</th>
                                        <th>Total Bookings</th>
                                        <th>Total Spent</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white me-3" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                                                        <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($user['email']); ?><br>
                                                <?php if ($user['phone']): ?>
                                                    <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $user['total_bookings']; ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-success"><?php echo formatPrice($user['total_spent']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewUserDetails(<?php echo $user['id']; ?>)"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" 
                                                            onclick="viewUserBookings(<?php echo $user['id']; ?>)"
                                                            title="View Bookings">
                                                        <i class="fas fa-ticket-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewUserDetails(userId) {
    alert('User details functionality would be implemented here for user ID: ' + userId);
}

function viewUserBookings(userId) {
    alert('User bookings functionality would be implemented here for user ID: ' + userId);
}
</script>

<?php include '../includes/footer.php'; ?>

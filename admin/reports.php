<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Reports';

// Get report data
$current_month = date('Y-m');
$current_year = date('Y');

// Monthly revenue
$stmt = $pdo->prepare("
    SELECT SUM(total_amount) as revenue 
    FROM bookings 
    WHERE payment_status = 'completed' 
    AND DATE_FORMAT(booking_date, '%Y-%m') = ?
");
$stmt->execute([$current_month]);
$monthly_revenue = $stmt->fetchColumn() ?: 0;

// Monthly bookings
$stmt = $pdo->prepare("
    SELECT COUNT(*) as bookings 
    FROM bookings 
    WHERE DATE_FORMAT(booking_date, '%Y-%m') = ?
");
$stmt->execute([$current_month]);
$monthly_bookings = $stmt->fetchColumn();

// Top events by bookings
$stmt = $pdo->prepare("
    SELECT e.title, COUNT(b.id) as booking_count, SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.status = 'confirmed'
    GROUP BY e.id, e.title
    ORDER BY booking_count DESC
    LIMIT 10
");
$stmt->execute();
$top_events = $stmt->fetchAll();

// Monthly revenue trend (last 6 months)
$stmt = $pdo->prepare("
    SELECT DATE_FORMAT(booking_date, '%Y-%m') as month, 
           SUM(total_amount) as revenue,
           COUNT(*) as bookings
    FROM bookings 
    WHERE payment_status = 'completed' 
    AND booking_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(booking_date, '%Y-%m')
    ORDER BY month ASC
");
$stmt->execute();
$monthly_trends = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Reports & Analytics
                </h1>
                <p class="mb-0 mt-2">View platform performance and generate reports</p>
            </div>
        </div>
    </div>
</div>

<!-- Reports Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link active" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Current Month Stats -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card stat-card bg-success">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo formatPrice($monthly_revenue); ?></div>
                            <div>This Month's Revenue</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card stat-card bg-info">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo $monthly_bookings; ?></div>
                            <div>This Month's Bookings</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Monthly Trends -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Revenue Trend (Last 6 Months)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($monthly_trends)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No revenue data available yet</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Month</th>
                                                <th>Bookings</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($monthly_trends as $trend): ?>
                                                <tr>
                                                    <td><?php echo date('F Y', strtotime($trend['month'] . '-01')); ?></td>
                                                    <td><?php echo $trend['bookings']; ?></td>
                                                    <td><?php echo formatPrice($trend['revenue']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Chart visualization would be implemented with a charting library like Chart.js
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Top Events -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-star me-2"></i>
                                Top Events by Bookings
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_events)): ?>
                                <p class="text-muted text-center py-3">No events data available</p>
                            <?php else: ?>
                                <?php foreach (array_slice($top_events, 0, 5) as $index => $event): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3 pb-3 <?php echo $index < 4 ? 'border-bottom' : ''; ?>">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($event['title']); ?></h6>
                                            <small class="text-muted"><?php echo $event['booking_count']; ?> bookings</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success"><?php echo formatPrice($event['revenue']); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Report Generation -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-export me-2"></i>
                                Generate Reports
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body text-center">
                                            <i class="fas fa-calendar-alt fa-2x text-primary mb-3"></i>
                                            <h6>Events Report</h6>
                                            <p class="text-muted small">Generate report of all events with booking statistics</p>
                                            <a href="generate_report.php?type=events" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-download me-1"></i>Generate
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body text-center">
                                            <i class="fas fa-ticket-alt fa-2x text-success mb-3"></i>
                                            <h6>Bookings Report</h6>
                                            <p class="text-muted small">Generate detailed report of all bookings and revenue</p>
                                            <a href="generate_report.php?type=bookings" class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-download me-1"></i>Generate
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body text-center">
                                            <i class="fas fa-users fa-2x text-info mb-3"></i>
                                            <h6>Users Report</h6>
                                            <p class="text-muted small">Generate report of registered users and their activity</p>
                                            <a href="generate_report.php?type=users" class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-download me-1"></i>Generate
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Custom Date Range -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>Custom Date Range Report</h6>
                                    <form class="row g-3" action="generate_report.php" method="GET">
                                        <div class="col-md-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="start_date" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="end_date" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="report_type" class="form-label">Report Type</label>
                                            <select class="form-select" id="report_type" name="type" required>
                                                <option value="">Select Type</option>
                                                <option value="revenue">Revenue Report</option>
                                                <option value="bookings">Bookings Report</option>
                                                <option value="events">Events Report</option>
                                                <option value="users">Users Report</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-chart-bar me-1"></i>Generate
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php include '../includes/footer.php'; ?>

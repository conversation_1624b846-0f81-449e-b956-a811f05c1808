<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$report_type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';
$start_date = isset($_GET['start_date']) ? sanitizeInput($_GET['start_date']) : '';
$end_date = isset($_GET['end_date']) ? sanitizeInput($_GET['end_date']) : '';

if (empty($report_type)) {
    redirect('reports.php');
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $report_type . '_report_' . date('Y-m-d') . '.csv"');
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Create output stream
$output = fopen('php://output', 'w');

switch ($report_type) {
    case 'events':
        generateEventsReport($output, $start_date, $end_date);
        break;
    case 'bookings':
        generateBookingsReport($output, $start_date, $end_date);
        break;
    case 'users':
        generateUsersReport($output, $start_date, $end_date);
        break;
    case 'revenue':
        generateRevenueReport($output, $start_date, $end_date);
        break;
    default:
        fputcsv($output, ['Error: Invalid report type']);
}

fclose($output);
exit;

function generateEventsReport($output, $start_date, $end_date) {
    global $pdo;
    
    // CSV headers
    fputcsv($output, [
        'Event ID', 'Title', 'Category', 'Date', 'Time', 'Venue', 'Location', 
        'Organizer', 'Price (XAF)', 'Total Tickets', 'Available Tickets', 
        'Tickets Sold', 'Revenue (XAF)', 'Status', 'Created Date'
    ]);
    
    // Build query
    $where_clause = "1=1";
    $params = [];
    
    if (!empty($start_date)) {
        $where_clause .= " AND e.event_date >= ?";
        $params[] = $start_date;
    }
    
    if (!empty($end_date)) {
        $where_clause .= " AND e.event_date <= ?";
        $params[] = $end_date;
    }
    
    $sql = "
        SELECT e.*, 
               COUNT(b.id) as tickets_sold,
               SUM(CASE WHEN b.status = 'confirmed' THEN b.total_amount ELSE 0 END) as revenue
        FROM events e
        LEFT JOIN bookings b ON e.id = b.event_id
        WHERE $where_clause
        GROUP BY e.id
        ORDER BY e.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['id'],
            $row['title'],
            $row['category'],
            $row['event_date'],
            $row['event_time'],
            $row['venue'],
            $row['location'],
            $row['organizer'],
            number_format($row['price'], 0),
            $row['total_tickets'],
            $row['available_tickets'],
            $row['tickets_sold'],
            number_format($row['revenue'], 0),
            $row['status'],
            $row['created_at']
        ]);
    }
}

function generateBookingsReport($output, $start_date, $end_date) {
    global $pdo;
    
    // CSV headers
    fputcsv($output, [
        'Booking ID', 'Reference', 'User', 'Email', 'Event', 'Event Date', 
        'Quantity', 'Amount (XAF)', 'Attendee Name', 'Attendee Email', 
        'Booking Date', 'Status', 'Payment Status'
    ]);
    
    // Build query
    $where_clause = "1=1";
    $params = [];
    
    if (!empty($start_date)) {
        $where_clause .= " AND DATE(b.booking_date) >= ?";
        $params[] = $start_date;
    }
    
    if (!empty($end_date)) {
        $where_clause .= " AND DATE(b.booking_date) <= ?";
        $params[] = $end_date;
    }
    
    $sql = "
        SELECT b.*, e.title as event_title, e.event_date,
               u.first_name, u.last_name, u.email as user_email
        FROM bookings b
        JOIN events e ON b.event_id = e.id
        JOIN users u ON b.user_id = u.id
        WHERE $where_clause
        ORDER BY b.booking_date DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['id'],
            $row['booking_reference'],
            $row['first_name'] . ' ' . $row['last_name'],
            $row['user_email'],
            $row['event_title'],
            $row['event_date'],
            $row['quantity'],
            number_format($row['total_amount'], 0),
            $row['attendee_name'],
            $row['attendee_email'],
            $row['booking_date'],
            $row['status'],
            $row['payment_status']
        ]);
    }
}

function generateUsersReport($output, $start_date, $end_date) {
    global $pdo;
    
    // CSV headers
    fputcsv($output, [
        'User ID', 'Username', 'Name', 'Email', 'Phone', 'Registration Date', 
        'Total Bookings', 'Total Spent (XAF)', 'Last Booking Date'
    ]);
    
    // Build query
    $where_clause = "u.role = 'user'";
    $params = [];
    
    if (!empty($start_date)) {
        $where_clause .= " AND DATE(u.created_at) >= ?";
        $params[] = $start_date;
    }
    
    if (!empty($end_date)) {
        $where_clause .= " AND DATE(u.created_at) <= ?";
        $params[] = $end_date;
    }
    
    $sql = "
        SELECT u.*, 
               COUNT(b.id) as total_bookings,
               SUM(CASE WHEN b.payment_status = 'completed' THEN b.total_amount ELSE 0 END) as total_spent,
               MAX(b.booking_date) as last_booking_date
        FROM users u
        LEFT JOIN bookings b ON u.id = b.user_id
        WHERE $where_clause
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['id'],
            $row['username'],
            $row['first_name'] . ' ' . $row['last_name'],
            $row['email'],
            $row['phone'],
            $row['created_at'],
            $row['total_bookings'],
            number_format($row['total_spent'], 0),
            $row['last_booking_date']
        ]);
    }
}

function generateRevenueReport($output, $start_date, $end_date) {
    global $pdo;
    
    // CSV headers
    fputcsv($output, [
        'Date', 'Total Bookings', 'Total Revenue (XAF)', 'Confirmed Bookings', 
        'Confirmed Revenue (XAF)', 'Pending Bookings', 'Cancelled Bookings'
    ]);
    
    // Build query
    $where_clause = "1=1";
    $params = [];
    
    if (!empty($start_date)) {
        $where_clause .= " AND DATE(booking_date) >= ?";
        $params[] = $start_date;
    }
    
    if (!empty($end_date)) {
        $where_clause .= " AND DATE(booking_date) <= ?";
        $params[] = $end_date;
    }
    
    $sql = "
        SELECT DATE(booking_date) as booking_date,
               COUNT(*) as total_bookings,
               SUM(total_amount) as total_revenue,
               SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_bookings,
               SUM(CASE WHEN status = 'confirmed' THEN total_amount ELSE 0 END) as confirmed_revenue,
               SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_bookings,
               SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_bookings
        FROM bookings
        WHERE $where_clause
        GROUP BY DATE(booking_date)
        ORDER BY booking_date DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['booking_date'],
            $row['total_bookings'],
            number_format($row['total_revenue'], 0),
            $row['confirmed_bookings'],
            number_format($row['confirmed_revenue'], 0),
            $row['pending_bookings'],
            $row['cancelled_bookings']
        ]);
    }
}
?>

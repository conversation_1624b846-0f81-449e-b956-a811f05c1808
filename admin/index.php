<?php
require_once '../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'Admin Dashboard';

// Get statistics
$stats = [];

// Total events
$stmt = $pdo->prepare("SELECT COUNT(*) FROM events");
$stmt->execute();
$stats['total_events'] = $stmt->fetchColumn();

// Active events
$stmt = $pdo->prepare("SELECT COUNT(*) FROM events WHERE status = 'active'");
$stmt->execute();
$stats['active_events'] = $stmt->fetchColumn();

// Total users
$stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'user'");
$stmt->execute();
$stats['total_users'] = $stmt->fetchColumn();

// Total bookings
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings");
$stmt->execute();
$stats['total_bookings'] = $stmt->fetchColumn();

// Total revenue
$stmt = $pdo->prepare("SELECT SUM(total_amount) FROM bookings WHERE payment_status = 'completed'");
$stmt->execute();
$stats['total_revenue'] = $stmt->fetchColumn() ?: 0;

// Recent bookings
$stmt = $pdo->prepare("
    SELECT b.*, e.title, u.first_name, u.last_name, u.email
    FROM bookings b
    JOIN events e ON b.event_id = e.id
    JOIN users u ON b.user_id = u.id
    ORDER BY b.booking_date DESC
    LIMIT 10
");
$stmt->execute();
$recent_bookings = $stmt->fetchAll();

// Popular events
$stmt = $pdo->prepare("
    SELECT e.title, COUNT(b.id) as booking_count, SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.status = 'confirmed'
    WHERE e.status = 'active'
    GROUP BY e.id, e.title
    ORDER BY booking_count DESC
    LIMIT 5
");
$stmt->execute();
$popular_events = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-dark text-white py-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Admin Dashboard
                </h1>
                <p class="mb-0 mt-2">Welcome to EventTopia Administration Panel</p>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content -->
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="my-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo $stats['total_events']; ?></div>
                            <div>Total Events</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card stat-card bg-success">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                            <div>Total Users</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card stat-card bg-info">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo $stats['total_bookings']; ?></div>
                            <div>Total Bookings</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card stat-card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                            <div class="stat-number"><?php echo formatPrice($stats['total_revenue']); ?></div>
                            <div>Total Revenue</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Recent Bookings -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Bookings
                            </h5>
                            <a href="bookings.php" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_bookings)): ?>
                                <p class="text-muted text-center py-3">No bookings yet</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Booking ID</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_bookings as $booking): ?>
                                                <tr>
                                                    <td>#<?php echo htmlspecialchars($booking['booking_reference']); ?></td>
                                                    <td>
                                                        <?php echo htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']); ?><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($booking['email']); ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($booking['title']); ?></td>
                                                    <td><?php echo formatPrice($booking['total_amount']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking['status'] === 'confirmed' ? 'success' : ($booking['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                            <?php echo ucfirst($booking['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('M j, Y', strtotime($booking['booking_date'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Popular Events -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-star me-2"></i>
                                Popular Events
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($popular_events)): ?>
                                <p class="text-muted text-center py-3">No events data available</p>
                            <?php else: ?>
                                <?php foreach ($popular_events as $event): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3 pb-3 border-bottom">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($event['title']); ?></h6>
                                            <small class="text-muted"><?php echo $event['booking_count']; ?> bookings</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success"><?php echo formatPrice($event['revenue']); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <a href="add_event.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                                        Add New Event
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="events.php" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-calendar-alt fa-2x mb-2 d-block"></i>
                                        Manage Events
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="bookings.php" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-ticket-alt fa-2x mb-2 d-block"></i>
                                        View Bookings
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="reports.php" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                        Generate Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

-- EventTopia Database Schema
CREATE DATABASE IF NOT EXISTS eventtopia;
USE eventtopia;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name VA<PERSON><PERSON>R(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Events table
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHA<PERSON>(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image VARCHAR(255),
    price DECIMAL(10,2) NOT NULL,
    available_tickets INT NOT NULL,
    total_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE,
    attendee_name VARCHAR(100),
    attendee_email VARCHAR(100),
    attendee_phone VARCHAR(20),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Cart table
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_event (user_id, event_id)
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin');

-- Insert sample events
INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, available_tickets, total_tickets, category) VALUES 
('Tech Conference 2024', 'Annual technology conference featuring latest innovations', '2024-03-15', '09:00:00', 'Convention Center', 'New York, NY', 'Tech Events Inc', '<EMAIL>', 299.99, 500, 500, 'Technology'),
('Music Festival', 'Three-day music festival with top artists', '2024-04-20', '18:00:00', 'Central Park', 'New York, NY', 'Music Productions', '<EMAIL>', 149.99, 1000, 1000, 'Music'),
('Food & Wine Expo', 'Culinary experience with renowned chefs', '2024-05-10', '12:00:00', 'Exhibition Hall', 'Los Angeles, CA', 'Culinary Events', '<EMAIL>', 89.99, 300, 300, 'Food'),
('Business Summit', 'Networking event for entrepreneurs', '2024-06-05', '08:30:00', 'Business Center', 'Chicago, IL', 'Business Network', '<EMAIL>', 199.99, 200, 200, 'Business');

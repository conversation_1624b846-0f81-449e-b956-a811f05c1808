<?php
// Simple PDF Ticket Generator using basic PHP
class TicketPDF {
    private $booking;
    private $event;
    
    public function __construct($booking, $event) {
        $this->booking = $booking;
        $this->event = $event;
    }
    
    public function generate() {
        // For now, we'll generate an HTML ticket that can be printed as PDF
        // Set headers for HTML download that can be saved as PDF
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="ticket_' . $this->booking['booking_reference'] . '.html"');

        $html = $this->generateTicketHTML();

        // Add print styles and auto-print functionality
        $html = str_replace('</head>', '
        <style media="print">
            body { margin: 0; }
            .no-print { display: none; }
        </style>
        <script>
            window.onload = function() {
                // Auto-print when page loads
                setTimeout(function() {
                    window.print();
                }, 1000);
            }
        </script>
        </head>', $html);

        echo $html;
    }
    
    private function generateTicketHTML() {
        $qr_code_url = "https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=" . urlencode($this->booking['booking_reference']);
        
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Event Ticket</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .ticket { border: 2px solid #0d6efd; border-radius: 10px; padding: 20px; max-width: 600px; }
                .header { text-align: center; border-bottom: 2px dashed #ccc; padding-bottom: 20px; margin-bottom: 20px; }
                .event-title { font-size: 24px; font-weight: bold; color: #0d6efd; margin-bottom: 10px; }
                .event-details { margin-bottom: 20px; }
                .detail-row { margin-bottom: 8px; }
                .label { font-weight: bold; color: #333; }
                .value { color: #666; }
                .qr-section { text-align: center; margin-top: 20px; border-top: 2px dashed #ccc; padding-top: 20px; }
                .booking-ref { font-size: 18px; font-weight: bold; color: #0d6efd; }
                .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #999; }
            </style>
        </head>
        <body>
            <div class="ticket">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; color: #0d6efd;">EventTopia</div>
                    <div style="font-size: 16px; color: #666;">Official Event Ticket</div>
                </div>
                
                <div class="event-title">' . htmlspecialchars($this->event['title']) . '</div>
                
                <div class="event-details">
                    <div class="detail-row">
                        <span class="label">Date:</span> 
                        <span class="value">' . date('F j, Y', strtotime($this->event['event_date'])) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Time:</span> 
                        <span class="value">' . date('g:i A', strtotime($this->event['event_time'])) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Venue:</span> 
                        <span class="value">' . htmlspecialchars($this->event['venue']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Location:</span> 
                        <span class="value">' . htmlspecialchars($this->event['location']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Organizer:</span> 
                        <span class="value">' . htmlspecialchars($this->event['organizer']) . '</span>
                    </div>
                </div>
                
                <div style="border: 1px solid #ddd; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <div class="detail-row">
                        <span class="label">Ticket Holder:</span> 
                        <span class="value">' . htmlspecialchars($this->booking['attendee_name']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Email:</span> 
                        <span class="value">' . htmlspecialchars($this->booking['attendee_email']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Quantity:</span> 
                        <span class="value">' . $this->booking['quantity'] . ' ticket(s)</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Total Amount:</span> 
                        <span class="value">' . number_format($this->booking['total_amount'], 0) . ' XAF</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Booking Date:</span> 
                        <span class="value">' . date('F j, Y g:i A', strtotime($this->booking['booking_date'])) . '</span>
                    </div>
                </div>
                
                <div class="qr-section">
                    <div class="booking-ref">Booking Reference: ' . htmlspecialchars($this->booking['booking_reference']) . '</div>
                    <div style="margin: 15px 0;">
                        <img src="' . $qr_code_url . '" alt="QR Code" style="border: 1px solid #ddd;">
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        Present this ticket at the venue entrance
                    </div>
                </div>
                
                <div class="footer">
                    <div>Generated on ' . date('F j, Y g:i A') . '</div>
                    <div>EventTopia - Your Premier Event Booking Platform</div>
                    <div style="margin-top: 10px; font-size: 10px;">
                        This ticket is valid only for the specified event and cannot be transferred or refunded.
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button onclick="window.print()" style="background: #0d6efd; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        Print Ticket
                    </button>
                    <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                        Close
                    </button>
                </div>
            </div>
        </body>
        </html>';
    }
    

}

// Function to generate and download ticket
function generateTicketPDF($booking_id) {
    global $pdo;
    
    // Get booking and event details
    $stmt = $pdo->prepare("
        SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.organizer
        FROM bookings b
        JOIN events e ON b.event_id = e.id
        WHERE b.id = ?
    ");
    $stmt->execute([$booking_id]);
    $booking = $stmt->fetch();
    
    if (!$booking) {
        return false;
    }
    
    $event = [
        'title' => $booking['title'],
        'event_date' => $booking['event_date'],
        'event_time' => $booking['event_time'],
        'venue' => $booking['venue'],
        'location' => $booking['location'],
        'organizer' => $booking['organizer']
    ];
    
    $ticket = new TicketPDF($booking, $event);
    $ticket->generate();
    return true;
}
?>

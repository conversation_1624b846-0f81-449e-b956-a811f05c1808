<?php
// Simple PDF Ticket Generator using basic PHP
class TicketPDF {
    private $booking;
    private $event;
    
    public function __construct($booking, $event) {
        $this->booking = $booking;
        $this->event = $event;
    }
    
    public function generate() {
        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="ticket_' . $this->booking['booking_reference'] . '.pdf"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        
        // Create a simple PDF using basic HTML to PDF conversion
        // For a production environment, you would use a proper PDF library like TCPDF or FPDF
        
        $html = $this->generateTicketHTML();
        
        // Simple HTML to PDF conversion (basic implementation)
        // In production, use libraries like TCPDF, FPDF, or DomPDF
        echo $this->convertHTMLToPDF($html);
    }
    
    private function generateTicketHTML() {
        $qr_code_url = "https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=" . urlencode($this->booking['booking_reference']);
        
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Event Ticket</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .ticket { border: 2px solid #0d6efd; border-radius: 10px; padding: 20px; max-width: 600px; }
                .header { text-align: center; border-bottom: 2px dashed #ccc; padding-bottom: 20px; margin-bottom: 20px; }
                .event-title { font-size: 24px; font-weight: bold; color: #0d6efd; margin-bottom: 10px; }
                .event-details { margin-bottom: 20px; }
                .detail-row { margin-bottom: 8px; }
                .label { font-weight: bold; color: #333; }
                .value { color: #666; }
                .qr-section { text-align: center; margin-top: 20px; border-top: 2px dashed #ccc; padding-top: 20px; }
                .booking-ref { font-size: 18px; font-weight: bold; color: #0d6efd; }
                .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #999; }
            </style>
        </head>
        <body>
            <div class="ticket">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; color: #0d6efd;">EventTopia</div>
                    <div style="font-size: 16px; color: #666;">Official Event Ticket</div>
                </div>
                
                <div class="event-title">' . htmlspecialchars($this->event['title']) . '</div>
                
                <div class="event-details">
                    <div class="detail-row">
                        <span class="label">Date:</span> 
                        <span class="value">' . date('F j, Y', strtotime($this->event['event_date'])) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Time:</span> 
                        <span class="value">' . date('g:i A', strtotime($this->event['event_time'])) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Venue:</span> 
                        <span class="value">' . htmlspecialchars($this->event['venue']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Location:</span> 
                        <span class="value">' . htmlspecialchars($this->event['location']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Organizer:</span> 
                        <span class="value">' . htmlspecialchars($this->event['organizer']) . '</span>
                    </div>
                </div>
                
                <div style="border: 1px solid #ddd; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <div class="detail-row">
                        <span class="label">Ticket Holder:</span> 
                        <span class="value">' . htmlspecialchars($this->booking['attendee_name']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Email:</span> 
                        <span class="value">' . htmlspecialchars($this->booking['attendee_email']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Quantity:</span> 
                        <span class="value">' . $this->booking['quantity'] . ' ticket(s)</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Total Amount:</span> 
                        <span class="value">' . number_format($this->booking['total_amount'], 0) . ' XAF</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Booking Date:</span> 
                        <span class="value">' . date('F j, Y g:i A', strtotime($this->booking['booking_date'])) . '</span>
                    </div>
                </div>
                
                <div class="qr-section">
                    <div class="booking-ref">Booking Reference: ' . htmlspecialchars($this->booking['booking_reference']) . '</div>
                    <div style="margin: 15px 0;">
                        <img src="' . $qr_code_url . '" alt="QR Code" style="border: 1px solid #ddd;">
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        Present this ticket at the venue entrance
                    </div>
                </div>
                
                <div class="footer">
                    <div>Generated on ' . date('F j, Y g:i A') . '</div>
                    <div>EventTopia - Your Premier Event Booking Platform</div>
                    <div style="margin-top: 10px; font-size: 10px;">
                        This ticket is valid only for the specified event and cannot be transferred or refunded.
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
    
    private function convertHTMLToPDF($html) {
        // This is a simplified implementation
        // In production, use proper PDF libraries like TCPDF, FPDF, or DomPDF
        
        // For now, we'll return the HTML content with PDF headers
        // The browser will handle the conversion or you can implement a proper PDF library
        
        // Basic PDF structure (simplified)
        $pdf_content = "%PDF-1.4\n";
        $pdf_content .= "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
        $pdf_content .= "2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n";
        $pdf_content .= "3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n";
        $pdf_content .= "4 0 obj\n<< /Length " . strlen($html) . " >>\nstream\n";
        $pdf_content .= "BT /F1 12 Tf 50 750 Td (" . strip_tags($html) . ") Tj ET\n";
        $pdf_content .= "endstream\nendobj\n";
        $pdf_content .= "xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000207 00000 n \n";
        $pdf_content .= "trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n" . strlen($pdf_content) . "\n%%EOF";
        
        // For demonstration, we'll return HTML content
        // In production, implement proper PDF generation
        return $html;
    }
}

// Function to generate and download ticket
function generateTicketPDF($booking_id) {
    global $pdo;
    
    // Get booking and event details
    $stmt = $pdo->prepare("
        SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.organizer
        FROM bookings b
        JOIN events e ON b.event_id = e.id
        WHERE b.id = ?
    ");
    $stmt->execute([$booking_id]);
    $booking = $stmt->fetch();
    
    if (!$booking) {
        return false;
    }
    
    $event = [
        'title' => $booking['title'],
        'event_date' => $booking['event_date'],
        'event_time' => $booking['event_time'],
        'venue' => $booking['venue'],
        'location' => $booking['location'],
        'organizer' => $booking['organizer']
    ];
    
    $ticket = new TicketPDF($booking, $event);
    $ticket->generate();
    return true;
}
?>

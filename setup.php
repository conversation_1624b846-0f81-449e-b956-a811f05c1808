<?php
/**
 * EventTopia Setup Script
 * This script helps with the initial setup of the EventTopia application
 */

// Check if setup has already been completed
if (file_exists('config/setup_complete.txt')) {
    die('Setup has already been completed. Delete config/setup_complete.txt to run setup again.');
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // Database configuration step
        $db_host = $_POST['db_host'];
        $db_name = $_POST['db_name'];
        $db_user = $_POST['db_user'];
        $db_pass = $_POST['db_pass'];
        
        // Test database connection
        try {
            $dsn = "mysql:host=$db_host;dbname=$db_name";
            $pdo = new PDO($dsn, $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Update database configuration file
            $config_content = "<?php\n";
            $config_content .= "// Database configuration for EventTopia\n";
            $config_content .= "define('DB_HOST', '$db_host');\n";
            $config_content .= "define('DB_USER', '$db_user');\n";
            $config_content .= "define('DB_PASS', '$db_pass');\n";
            $config_content .= "define('DB_NAME', '$db_name');\n\n";
            $config_content .= file_get_contents('config/database.php');
            $config_content = preg_replace('/define\(\'DB_HOST\', \'.*?\'\);/', "define('DB_HOST', '$db_host');", $config_content);
            $config_content = preg_replace('/define\(\'DB_USER\', \'.*?\'\);/', "define('DB_USER', '$db_user');", $config_content);
            $config_content = preg_replace('/define\(\'DB_PASS\', \'.*?\'\);/', "define('DB_PASS', '$db_pass');", $config_content);
            $config_content = preg_replace('/define\(\'DB_NAME\', \'.*?\'\);/', "define('DB_NAME', '$db_name');", $config_content);
            
            file_put_contents('config/database.php', $config_content);
            
            header('Location: setup.php?step=2');
            exit;
            
        } catch (PDOException $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Import database schema
        try {
            require_once 'config/database.php';
            $database = new Database();
            $pdo = $database->getConnection();
            
            // Read and execute SQL file
            $sql = file_get_contents('database.sql');
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            header('Location: setup.php?step=3');
            exit;
            
        } catch (Exception $e) {
            $error = 'Database setup failed: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Create admin account
        $admin_email = $_POST['admin_email'];
        $admin_password = $_POST['admin_password'];
        $admin_first_name = $_POST['admin_first_name'];
        $admin_last_name = $_POST['admin_last_name'];
        
        if (strlen($admin_password) < 6) {
            $error = 'Password must be at least 6 characters long';
        } else {
            try {
                require_once 'config/database.php';
                $database = new Database();
                $pdo = $database->getConnection();
                
                // Update admin user
                $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ?, first_name = ?, last_name = ? WHERE role = 'admin'");
                $stmt->execute([$admin_email, $hashed_password, $admin_first_name, $admin_last_name]);
                
                header('Location: setup.php?step=4');
                exit;
                
            } catch (Exception $e) {
                $error = 'Admin account setup failed: ' . $e->getMessage();
            }
        }
    } elseif ($step == 4) {
        // Complete setup
        file_put_contents('config/setup_complete.txt', date('Y-m-d H:i:s'));
        header('Location: index.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventTopia Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            EventTopia Setup
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Progress Bar -->
                        <div class="progress mb-4">
                            <div class="progress-bar" role="progressbar" style="width: <?php echo ($step / 4) * 100; ?>%">
                                Step <?php echo $step; ?> of 4
                            </div>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($step == 1): ?>
                            <!-- Step 1: Database Configuration -->
                            <h4 class="mb-3">
                                <i class="fas fa-database me-2"></i>
                                Database Configuration
                            </h4>
                            <p class="text-muted mb-4">Enter your database connection details.</p>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="db_host" class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_name" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="eventtopia" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_user" class="form-label">Database Username</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_pass" class="form-label">Database Password</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    Test Connection & Continue
                                </button>
                            </form>
                            
                        <?php elseif ($step == 2): ?>
                            <!-- Step 2: Database Setup -->
                            <h4 class="mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                Database Setup
                            </h4>
                            <p class="text-muted mb-4">Click the button below to create the database tables and import sample data.</p>
                            
                            <form method="POST">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    This will create all necessary tables and import sample events.
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-database me-2"></i>
                                    Setup Database
                                </button>
                            </form>
                            
                        <?php elseif ($step == 3): ?>
                            <!-- Step 3: Admin Account -->
                            <h4 class="mb-3">
                                <i class="fas fa-user-shield me-2"></i>
                                Admin Account Setup
                            </h4>
                            <p class="text-muted mb-4">Create your administrator account.</p>
                            
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="admin_first_name" class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="admin_first_name" name="admin_first_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="admin_last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="admin_last_name" name="admin_last_name" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="admin_email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="admin_password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                    <small class="text-muted">Minimum 6 characters</small>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Create Admin Account
                                </button>
                            </form>
                            
                        <?php elseif ($step == 4): ?>
                            <!-- Step 4: Complete -->
                            <h4 class="mb-3">
                                <i class="fas fa-check-circle me-2 text-success"></i>
                                Setup Complete!
                            </h4>
                            <p class="text-muted mb-4">EventTopia has been successfully installed and configured.</p>
                            
                            <div class="alert alert-success">
                                <h6>What's Next?</h6>
                                <ul class="mb-0">
                                    <li>Visit your EventTopia homepage</li>
                                    <li>Login to the admin panel to manage events</li>
                                    <li>Customize the appearance and settings</li>
                                    <li>Add your own events and start promoting!</li>
                                </ul>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-home me-2"></i>
                                    Go to EventTopia
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Help Text -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        Need help? Check the README.md file for detailed installation instructions.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

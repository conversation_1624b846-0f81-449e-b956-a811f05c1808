<?php
require_once 'config/config.php';
require_once 'includes/pdf_ticket.php';

// Require login
requireLogin();

$booking_id = isset($_GET['booking_id']) ? (int)$_GET['booking_id'] : 0;

if (!$booking_id) {
    redirect('user/booking_history.php');
}

// Verify booking belongs to user (or is admin)
$stmt = $pdo->prepare("SELECT id, user_id, status FROM bookings WHERE id = ?");
$stmt->execute([$booking_id]);
$booking = $stmt->fetch();

if (!$booking || ($booking['user_id'] != $_SESSION['user_id'] && !isAdmin())) {
    redirect('user/booking_history.php');
}

if ($booking['status'] !== 'confirmed') {
    $_SESSION['error'] = 'Tickets can only be downloaded for confirmed bookings';
    redirect('user/booking_history.php');
}

// Generate and download ticket
if (generateTicketPDF($booking_id)) {
    exit;
} else {
    $_SESSION['error'] = 'Failed to generate ticket';
    redirect('user/booking_history.php');
}
?>

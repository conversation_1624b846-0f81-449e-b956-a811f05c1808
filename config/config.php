<?php
// Application configuration
session_start();

// Site settings
define('SITE_NAME', 'EventTopia');
define('SITE_URL', 'http://localhost');
define('SITE_EMAIL', '<EMAIL>');

// Upload settings
define('UPLOAD_PATH', 'assets/images/events/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Pagination settings
define('EVENTS_PER_PAGE', 12);
define('BOOKINGS_PER_PAGE', 10);

// Include database connection
require_once 'database.php';

// Initialize database connection
$database = new Database();
$pdo = $database->getConnection();

// Helper functions
function redirect($url) {
    // Clean any output buffers
    if (ob_get_level()) {
        ob_end_clean();
    }

    // If URL doesn't start with http, make it relative to site root
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = SITE_URL . '/' . ltrim($url, '/');
    }

    header("Location: " . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect('login.php');
    }
}

function requireAdmin() {
    if (!isLoggedIn()) {
        redirect('adminlogin.php');
    } elseif (!isAdmin()) {
        redirect('index.php');
    }
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generateBookingReference() {
    return 'ET' . date('Ymd') . rand(1000, 9999);
}

function formatPrice($price) {
    return number_format($price, 0) . ' XAF';
}

function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

function formatTime($time) {
    return date('g:i A', strtotime($time));
}
?>

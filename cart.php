<?php
require_once 'config/config.php';

// Require login
requireLogin();

$page_title = 'Shopping Cart';

// Get cart items
$stmt = $pdo->prepare("
    SELECT c.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.price, e.image, e.available_tickets
    FROM cart c
    JOIN events e ON c.event_id = e.id
    WHERE c.user_id = ? AND e.status = 'active'
    ORDER BY c.added_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$cart_items = $stmt->fetchAll();

// Calculate totals
$subtotal = 0;
foreach ($cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}

$tax_rate = 0.08; // 8% tax
$tax_amount = $subtotal * $tax_rate;
$total = $subtotal + $tax_amount;

include 'includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Shopping Cart
                </h1>
            </div>
        </div>
    </div>
</div>

<!-- Cart Content -->
<section class="py-5">
    <div class="container">
        <?php if (empty($cart_items)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                            <h3>Your cart is empty</h3>
                            <p class="text-muted mb-4">Looks like you haven't added any events to your cart yet.</p>
                            <a href="events.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>
                                Browse Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">Cart Items (<?php echo count($cart_items); ?>)</h4>
                        </div>
                        <div class="card-body p-0">
                            <?php foreach ($cart_items as $item): ?>
                                <div class="cart-item border-bottom p-4">
                                    <div class="row align-items-center">
                                        <!-- Event Image -->
                                        <div class="col-md-3">
                                            <?php if ($item['image']): ?>
                                                <img src="<?php echo htmlspecialchars($item['image']); ?>" 
                                                     class="img-fluid rounded" 
                                                     alt="<?php echo htmlspecialchars($item['title']); ?>"
                                                     style="height: 100px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-primary d-flex align-items-center justify-content-center text-white rounded" 
                                                     style="height: 100px;">
                                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Event Details -->
                                        <div class="col-md-4">
                                            <h5 class="mb-1"><?php echo htmlspecialchars($item['title']); ?></h5>
                                            <p class="text-muted mb-1">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo formatDate($item['event_date']) . ' at ' . formatTime($item['event_time']); ?>
                                            </p>
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?php echo htmlspecialchars($item['venue']); ?>
                                            </p>
                                        </div>
                                        
                                        <!-- Quantity Controls -->
                                        <div class="col-md-2">
                                            <div class="quantity-controls">
                                                <button class="quantity-btn" data-action="decrease" data-cart-id="<?php echo $item['id']; ?>">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <span class="quantity-display mx-2"><?php echo $item['quantity']; ?></span>
                                                <button class="quantity-btn" data-action="increase" data-cart-id="<?php echo $item['id']; ?>">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted d-block mt-1">
                                                Max: <?php echo $item['available_tickets']; ?>
                                            </small>
                                        </div>
                                        
                                        <!-- Price -->
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <div class="fw-bold"><?php echo formatPrice($item['price'] * $item['quantity']); ?></div>
                                                <small class="text-muted"><?php echo formatPrice($item['price']); ?> each</small>
                                            </div>
                                        </div>
                                        
                                        <!-- Remove Button -->
                                        <div class="col-md-1">
                                            <button class="btn btn-outline-danger btn-sm remove-from-cart" 
                                                    data-cart-id="<?php echo $item['id']; ?>"
                                                    title="Remove from cart">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Continue Shopping -->
                    <div class="mt-3">
                        <a href="events.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Continue Shopping
                        </a>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">Order Summary</h4>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span><?php echo formatPrice($subtotal); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax (8%):</span>
                                <span><?php echo formatPrice($tax_amount); ?></span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total:</strong>
                                <strong class="text-success"><?php echo formatPrice($total); ?></strong>
                            </div>
                            
                            <a href="checkout.php" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-credit-card me-2"></i>
                                Proceed to Checkout
                            </a>
                        </div>
                    </div>
                    
                    <!-- Security Notice -->
                    <div class="card mt-3">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                            <h6>Secure Checkout</h6>
                            <small class="text-muted">
                                Your payment information is encrypted and secure.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

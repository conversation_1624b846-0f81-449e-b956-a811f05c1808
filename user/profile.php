<?php
require_once '../config/config.php';

// Require login
requireLogin();

$page_title = 'Profile';

$error = '';
$success = '';

// Get user data
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Handle profile picture upload
    $uploaded_profile_pic = '';
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] == 0) {
        $upload_dir = '../assets/images/profiles/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $file_extension = strtolower(pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($file_extension, $allowed_extensions)) {
            if ($_FILES['profile_picture']['size'] <= MAX_FILE_SIZE) {
                $filename = 'profile_' . $_SESSION['user_id'] . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;

                if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                    $uploaded_profile_pic = 'assets/images/profiles/' . $filename;

                    // Delete old profile picture if exists
                    if (!empty($user['profile_picture']) && file_exists('../' . $user['profile_picture'])) {
                        unlink('../' . $user['profile_picture']);
                    }
                } else {
                    $error = 'Failed to upload profile picture';
                }
            } else {
                $error = 'Profile picture is too large. Maximum size is 5MB';
            }
        } else {
            $error = 'Invalid image format. Only JPG, JPEG, PNG, and GIF are allowed';
        }
    }
    
    // Basic validation
    if (!isset($error) && (empty($first_name) || empty($last_name) || empty($email))) {
        $error = 'Please fill in all required fields';
    } elseif (!isset($error)) {
        try {
            // Check if email is already taken by another user
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $_SESSION['user_id']]);

            if ($stmt->fetch()) {
                $error = 'Email address is already taken by another user';
            } else {
                // Update basic information including profile picture
                if (!empty($uploaded_profile_pic)) {
                    $stmt = $pdo->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, profile_picture = ? WHERE id = ?");
                    $stmt->execute([$first_name, $last_name, $email, $phone, $address, $uploaded_profile_pic, $_SESSION['user_id']]);
                } else {
                    $stmt = $pdo->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ? WHERE id = ?");
                    $stmt->execute([$first_name, $last_name, $email, $phone, $address, $_SESSION['user_id']]);
                }
                
                // Update session data
                $_SESSION['first_name'] = $first_name;
                $_SESSION['last_name'] = $last_name;
                $_SESSION['email'] = $email;
                
                // Handle password change
                if (!empty($current_password) || !empty($new_password)) {
                    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                        $error = 'Please fill in all password fields to change your password';
                    } elseif ($new_password !== $confirm_password) {
                        $error = 'New passwords do not match';
                    } elseif (strlen($new_password) < 6) {
                        $error = 'New password must be at least 6 characters long';
                    } elseif (!password_verify($current_password, $user['password'])) {
                        $error = 'Current password is incorrect';
                    } else {
                        // Update password
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                        $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                        
                        $success = 'Profile and password updated successfully!';
                    }
                } else {
                    $success = 'Profile updated successfully!';
                }
                
                // Refresh user data
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch();
            }
        } catch (Exception $e) {
            $error = 'An error occurred while updating your profile';
        }
    }
}

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-user me-2"></i>
                    My Profile
                </h1>
                <p class="mb-0 mt-2">Manage your account information and preferences</p>
            </div>
        </div>
    </div>
</div>

<!-- Profile Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Edit Profile Information
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" enctype="multipart/form-data">
                            <!-- Profile Picture -->
                            <h5 class="mb-3">Profile Picture</h5>
                            <div class="row mb-4">
                                <div class="col-md-3 text-center">
                                    <?php if (!empty($user['profile_picture'])): ?>
                                        <img src="../<?php echo htmlspecialchars($user['profile_picture']); ?>"
                                             class="rounded-circle img-fluid mb-3"
                                             style="width: 120px; height: 120px; object-fit: cover;"
                                             alt="Profile Picture">
                                    <?php else: ?>
                                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white mb-3"
                                             style="width: 120px; height: 120px;">
                                            <i class="fas fa-user fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-9">
                                    <label for="profile_picture" class="form-label">Upload New Profile Picture</label>
                                    <input type="file" class="form-control" id="profile_picture" name="profile_picture"
                                           accept="image/*">
                                    <small class="text-muted">Max size: 5MB. Formats: JPG, PNG, GIF</small>
                                </div>
                            </div>

                            <hr>

                            <!-- Basic Information -->
                            <h5 class="mb-3">Basic Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($user['phone']); ?>">
                            </div>
                            
                            <div class="mb-4">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($user['address']); ?></textarea>
                            </div>
                            
                            <hr>
                            
                            <!-- Password Change -->
                            <h5 class="mb-3">Change Password</h5>
                            <p class="text-muted mb-3">Leave password fields empty if you don't want to change your password.</p>
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                    <small class="text-muted">Minimum 6 characters</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Update Profile
                                </button>
                                <a href="dashboard.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Profile Summary -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Account Summary
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <?php if (!empty($user['profile_picture'])): ?>
                                <img src="../<?php echo htmlspecialchars($user['profile_picture']); ?>"
                                     class="rounded-circle img-fluid mb-3"
                                     style="width: 80px; height: 80px; object-fit: cover;"
                                     alt="Profile Picture">
                            <?php else: ?>
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white mb-3"
                                     style="width: 80px; height: 80px;">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                            <?php endif; ?>
                            <h5 class="mb-1"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h5>
                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['email']); ?></p>
                        </div>
                        
                        <div class="border-top pt-3">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <?php
                                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE user_id = ?");
                                    $stmt->execute([$_SESSION['user_id']]);
                                    $total_bookings = $stmt->fetchColumn();
                                    ?>
                                    <div class="fw-bold text-primary"><?php echo $total_bookings; ?></div>
                                    <small class="text-muted">Total Bookings</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <?php
                                    $stmt = $pdo->prepare("
                                        SELECT COUNT(*) FROM bookings b 
                                        JOIN events e ON b.event_id = e.id 
                                        WHERE b.user_id = ? AND e.event_date >= CURDATE() AND b.status = 'confirmed'
                                    ");
                                    $stmt->execute([$_SESSION['user_id']]);
                                    $upcoming_events = $stmt->fetchColumn();
                                    ?>
                                    <div class="fw-bold text-success"><?php echo $upcoming_events; ?></div>
                                    <small class="text-muted">Upcoming Events</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border-top pt-3">
                            <h6 class="mb-2">Account Information</h6>
                            <small class="text-muted">
                                <strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?><br>
                                <strong>Member since:</strong> <?php echo date('F Y', strtotime($user['created_at'])); ?><br>
                                <strong>Account type:</strong> <?php echo ucfirst($user['role']); ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="booking_history.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-history me-2"></i>
                                View Booking History
                            </a>
                            <a href="../events.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-search me-2"></i>
                                Browse Events
                            </a>
                            <a href="../cart.php" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-shopping-cart me-2"></i>
                                View Cart
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>

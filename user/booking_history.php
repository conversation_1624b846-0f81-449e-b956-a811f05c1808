<?php
require_once '../config/config.php';

// Require login
requireLogin();

$page_title = 'Booking History';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * BOOKINGS_PER_PAGE;

// Get total bookings count
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$total_bookings = $stmt->fetchColumn();
$total_pages = ceil($total_bookings / BOOKINGS_PER_PAGE);

// Get bookings
$stmt = $pdo->prepare("
    SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.image, e.organizer
    FROM bookings b
    JOIN events e ON b.event_id = e.id
    WHERE b.user_id = ?
    ORDER BY b.booking_date DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([$_SESSION['user_id'], BOOKINGS_PER_PAGE, $offset]);
$bookings = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<!-- Page Header -->
<div class="bg-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-history me-2"></i>
                    Booking History
                </h1>
                <p class="mb-0 mt-2">View and manage all your event bookings</p>
            </div>
        </div>
    </div>
</div>

<!-- Booking History Content -->
<section class="py-5">
    <div class="container">
        <!-- Summary Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary"><?php echo $total_bookings; ?></h3>
                        <p class="mb-0">Total Bookings</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <?php
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM bookings b 
                            JOIN events e ON b.event_id = e.id 
                            WHERE b.user_id = ? AND e.event_date >= CURDATE() AND b.status = 'confirmed'
                        ");
                        $stmt->execute([$_SESSION['user_id']]);
                        $upcoming = $stmt->fetchColumn();
                        ?>
                        <h3 class="text-success"><?php echo $upcoming; ?></h3>
                        <p class="mb-0">Upcoming Events</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <?php
                        $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM bookings WHERE user_id = ? AND payment_status = 'completed'");
                        $stmt->execute([$_SESSION['user_id']]);
                        $total_spent = $stmt->fetchColumn() ?: 0;
                        ?>
                        <h3 class="text-info"><?php echo formatPrice($total_spent); ?></h3>
                        <p class="mb-0">Total Spent</p>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if (empty($bookings)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-ticket-alt fa-5x text-muted mb-4"></i>
                            <h3>No bookings yet</h3>
                            <p class="text-muted mb-4">You haven't booked any events yet. Start exploring amazing events!</p>
                            <a href="../events.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>
                                Browse Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Bookings List -->
            <div class="row">
                <div class="col-12">
                    <?php foreach ($bookings as $booking): ?>
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <!-- Event Image -->
                                    <div class="col-md-2">
                                        <?php if ($booking['image']): ?>
                                            <img src="../<?php echo htmlspecialchars($booking['image']); ?>" 
                                                 class="img-fluid rounded" 
                                                 alt="<?php echo htmlspecialchars($booking['title']); ?>"
                                                 style="height: 80px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-primary d-flex align-items-center justify-content-center text-white rounded" 
                                                 style="height: 80px;">
                                                <i class="fas fa-calendar-alt fa-2x"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Event Details -->
                                    <div class="col-md-5">
                                        <h5 class="mb-1"><?php echo htmlspecialchars($booking['title']); ?></h5>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($booking['event_date']) . ' at ' . formatTime($booking['event_time']); ?>
                                        </p>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($booking['venue']) . ', ' . htmlspecialchars($booking['location']); ?>
                                        </p>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-user me-1"></i>
                                            Organized by <?php echo htmlspecialchars($booking['organizer']); ?>
                                        </p>
                                    </div>
                                    
                                    <!-- Booking Info -->
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="mb-2">
                                                <span class="badge bg-<?php echo $booking['status'] === 'confirmed' ? 'success' : ($booking['status'] === 'pending' ? 'warning' : 'danger'); ?> fs-6">
                                                    <?php echo ucfirst($booking['status']); ?>
                                                </span>
                                            </div>
                                            <div class="fw-bold text-success mb-1"><?php echo formatPrice($booking['total_amount']); ?></div>
                                            <small class="text-muted"><?php echo $booking['quantity']; ?> ticket(s)</small><br>
                                            <small class="text-muted">Ref: <?php echo htmlspecialchars($booking['booking_reference']); ?></small>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="col-md-2 text-center">
                                        <div class="btn-group-vertical" role="group">
                                            <button class="btn btn-sm btn-outline-primary mb-1" 
                                                    onclick="viewBookingDetails(<?php echo $booking['id']; ?>)">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <?php if ($booking['status'] === 'confirmed'): ?>
                                                <button class="btn btn-sm btn-outline-success mb-1" 
                                                        onclick="downloadTicket(<?php echo $booking['id']; ?>)">
                                                    <i class="fas fa-download"></i> Ticket
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($booking['status'] === 'confirmed' && strtotime($booking['event_date']) > time()): ?>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="cancelBooking(<?php echo $booking['id']; ?>)">
                                                    <i class="fas fa-times"></i> Cancel
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Booking Date -->
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            Booked on <?php echo date('F j, Y \a\t g:i A', strtotime($booking['booking_date'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="Booking history pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <!-- Back to Dashboard -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Booking Details Modal -->
<div class="modal fade" id="bookingDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Booking Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bookingDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewBookingDetails(bookingId) {
    // This would typically load booking details via AJAX
    $('#bookingDetailsModal').modal('show');
    $('#bookingDetailsContent').html('<div class="text-center"><div class="spinner"></div></div>');
    
    // Simulate loading
    setTimeout(function() {
        $('#bookingDetailsContent').html('<p>Booking details would be displayed here. This is a demo implementation.</p>');
    }, 1000);
}

function downloadTicket(bookingId) {
    // This would typically generate and download a PDF ticket
    showAlert('info', 'Ticket download functionality would be implemented here.');
}

function cancelBooking(bookingId) {
    if (confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
        // This would typically send a cancellation request
        showAlert('info', 'Booking cancellation functionality would be implemented here.');
    }
}
</script>

<?php include '../includes/footer.php'; ?>

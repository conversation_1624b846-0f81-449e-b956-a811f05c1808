<?php
require_once '../config/config.php';

// Require login
requireLogin();

$page_title = 'Dashboard';

// Get user statistics
$user_id = $_SESSION['user_id'];

// Total bookings
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE user_id = ?");
$stmt->execute([$user_id]);
$total_bookings = $stmt->fetchColumn();

// Upcoming events
$stmt = $pdo->prepare("
    SELECT COUNT(*) FROM bookings b 
    JOIN events e ON b.event_id = e.id 
    WHERE b.user_id = ? AND e.event_date >= CURDATE() AND b.status = 'confirmed'
");
$stmt->execute([$user_id]);
$upcoming_events = $stmt->fetchColumn();

// Total spent
$stmt = $pdo->prepare("SELECT SUM(total_amount) FROM bookings WHERE user_id = ? AND payment_status = 'completed'");
$stmt->execute([$user_id]);
$total_spent = $stmt->fetchColumn() ?: 0;

// Recent bookings
$stmt = $pdo->prepare("
    SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.image
    FROM bookings b
    JOIN events e ON b.event_id = e.id
    WHERE b.user_id = ?
    ORDER BY b.booking_date DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_bookings = $stmt->fetchAll();

// Upcoming events details
$stmt = $pdo->prepare("
    SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.image
    FROM bookings b
    JOIN events e ON b.event_id = e.id
    WHERE b.user_id = ? AND e.event_date >= CURDATE() AND b.status = 'confirmed'
    ORDER BY e.event_date ASC
    LIMIT 3
");
$stmt->execute([$user_id]);
$upcoming_events_details = $stmt->fetchAll();

include '../includes/header.php';
?>

<!-- Alert Container -->
<div id="alertContainer"></div>

<?php if (isset($_SESSION['checkout_success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Booking Successful!</strong> Your tickets have been confirmed. Check your email for details.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['checkout_success']); ?>
<?php endif; ?>

<!-- Page Header -->
<div class="bg-primary text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Welcome back, <?php echo htmlspecialchars($_SESSION['first_name']); ?>!
                </h1>
                <p class="mb-0 mt-2">Manage your bookings and discover new events</p>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content -->
<section class="py-5">
    <div class="container">
        <!-- Statistics Cards -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card dashboard-card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                        <div class="stat-number"><?php echo $total_bookings; ?></div>
                        <div>Total Bookings</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card dashboard-card stat-card bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-3x mb-3"></i>
                        <div class="stat-number"><?php echo $upcoming_events; ?></div>
                        <div>Upcoming Events</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card dashboard-card stat-card bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                        <div class="stat-number"><?php echo formatPrice($total_spent); ?></div>
                        <div>Total Spent</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Upcoming Events -->
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Upcoming Events
                        </h4>
                        <a href="booking_history.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_events_details)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No upcoming events</p>
                                <a href="../events.php" class="btn btn-primary">Browse Events</a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($upcoming_events_details as $booking): ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <div class="flex-shrink-0 me-3">
                                        <?php if ($booking['image']): ?>
                                            <img src="../<?php echo htmlspecialchars($booking['image']); ?>" 
                                                 class="rounded" 
                                                 style="width: 60px; height: 60px; object-fit: cover;"
                                                 alt="<?php echo htmlspecialchars($booking['title']); ?>">
                                        <?php else: ?>
                                            <div class="bg-primary d-flex align-items-center justify-content-center text-white rounded" 
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($booking['title']); ?></h6>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($booking['event_date']) . ' at ' . formatTime($booking['event_time']); ?>
                                        </small><br>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($booking['venue']); ?>
                                        </small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-success"><?php echo $booking['quantity']; ?> tickets</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Recent Bookings -->
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Recent Bookings
                        </h4>
                        <a href="booking_history.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_bookings)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No bookings yet</p>
                                <a href="../events.php" class="btn btn-primary">Book Your First Event</a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_bookings as $booking): ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($booking['title']); ?></h6>
                                        <small class="text-muted">
                                            Booking #<?php echo htmlspecialchars($booking['booking_reference']); ?>
                                        </small><br>
                                        <small class="text-muted">
                                            Booked on <?php echo date('M j, Y', strtotime($booking['booking_date'])); ?>
                                        </small>
                                    </div>
                                    <div class="flex-shrink-0 text-end">
                                        <div class="fw-bold"><?php echo formatPrice($booking['total_amount']); ?></div>
                                        <span class="badge bg-<?php echo $booking['status'] === 'confirmed' ? 'success' : ($booking['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst($booking['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <a href="../events.php" class="btn btn-outline-primary btn-lg w-100">
                                    <i class="fas fa-search fa-2x mb-2 d-block"></i>
                                    Browse Events
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="booking_history.php" class="btn btn-outline-success btn-lg w-100">
                                    <i class="fas fa-history fa-2x mb-2 d-block"></i>
                                    View Bookings
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="profile.php" class="btn btn-outline-info btn-lg w-100">
                                    <i class="fas fa-user fa-2x mb-2 d-block"></i>
                                    Edit Profile
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../cart.php" class="btn btn-outline-warning btn-lg w-100">
                                    <i class="fas fa-shopping-cart fa-2x mb-2 d-block"></i>
                                    View Cart
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>
